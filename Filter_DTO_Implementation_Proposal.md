# Návrh implementace Filter DTO objektů

Tento dokument obsahuje návrh na implementaci jednoduchých filter DTO objektů, které by um<PERSON><PERSON><PERSON><PERSON> skutečné filtrování přes POST /entities/query endpoint bez problémů se serializací Expression objektů.

## 🎯 Cíl

Vytvořit jednoduché DTO objekty, které:
1. Lze snadno serializovat do/z JSON
2. Umož<PERSON>uj<PERSON> základní filtrování bez Expression objektů
3. Podporují řazení a stránkování
4. Jsou typově bezpečné a snadno použitelné

## 📋 Návrh struktury

### Základní FilterDto<T>

```csharp
public abstract class BaseFilterDto
{
    /// <summary>
    /// Pole pro řazení (název vlastnosti)
    /// </summary>
    public string? SortBy { get; set; }
    
    /// <summary>
    /// Směr <PERSON> (true = sestupně, false = vzestupně)
    /// </summary>
    public bool SortDescending { get; set; } = false;
    
    /// <summary>
    /// Číslo stránky (pro stránkování)
    /// </summary>
    public int? PageNumber { get; set; }
    
    /// <summary>
    /// Velikost stránky (pro stránkování)
    /// </summary>
    public int? PageSize { get; set; }
}
```

### SampleEntityFilterDto

```csharp
public class SampleEntityFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filtr podle názvu (obsahuje text)
    /// </summary>
    public string? NameFilter { get; set; }
    
    /// <summary>
    /// Filtr podle stavu aktivity
    /// </summary>
    public bool? IsActive { get; set; }
    
    /// <summary>
    /// Filtr podle věku (minimální věk)
    /// </summary>
    public int? MinAge { get; set; }
    
    /// <summary>
    /// Filtr podle věku (maximální věk)
    /// </summary>
    public int? MaxAge { get; set; }
    
    /// <summary>
    /// Filtr podle data vytvoření (od)
    /// </summary>
    public DateTime? CreatedAfter { get; set; }
    
    /// <summary>
    /// Filtr podle data vytvoření (do)
    /// </summary>
    public DateTime? CreatedBefore { get; set; }
    
    /// <summary>
    /// Filtr podle autora vytvoření
    /// </summary>
    public string? CreatedBy { get; set; }
}
```

### OrderFilterDto

```csharp
public class OrderFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filtr podle čísla objednávky
    /// </summary>
    public string? OrderNumber { get; set; }
    
    /// <summary>
    /// Filtr podle jména zákazníka
    /// </summary>
    public string? CustomerName { get; set; }
    
    /// <summary>
    /// Filtr podle emailu zákazníka
    /// </summary>
    public string? CustomerEmail { get; set; }
    
    /// <summary>
    /// Filtr podle stavu objednávky
    /// </summary>
    public OrderStatus? Status { get; set; }
    
    /// <summary>
    /// Filtr podle data objednávky (od)
    /// </summary>
    public DateTime? OrderDateFrom { get; set; }
    
    /// <summary>
    /// Filtr podle data objednávky (do)
    /// </summary>
    public DateTime? OrderDateTo { get; set; }
    
    /// <summary>
    /// Minimální celková částka
    /// </summary>
    public decimal? MinTotalAmount { get; set; }
    
    /// <summary>
    /// Maximální celková částka
    /// </summary>
    public decimal? MaxTotalAmount { get; set; }
    
    /// <summary>
    /// Filtr podle měny
    /// </summary>
    public string? Currency { get; set; }
}
```

### OrderItemFilterDto

```csharp
public class OrderItemFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filtr podle ID objednávky
    /// </summary>
    public Guid? OrderId { get; set; }
    
    /// <summary>
    /// Filtr podle kódu produktu
    /// </summary>
    public string? ProductCode { get; set; }
    
    /// <summary>
    /// Filtr podle názvu produktu
    /// </summary>
    public string? ProductName { get; set; }
    
    /// <summary>
    /// Filtr podle kategorie
    /// </summary>
    public string? Category { get; set; }
    
    /// <summary>
    /// Minimální jednotková cena
    /// </summary>
    public decimal? MinUnitPrice { get; set; }
    
    /// <summary>
    /// Maximální jednotková cena
    /// </summary>
    public decimal? MaxUnitPrice { get; set; }
    
    /// <summary>
    /// Minimální množství
    /// </summary>
    public int? MinQuantity { get; set; }
    
    /// <summary>
    /// Maximální množství
    /// </summary>
    public int? MaxQuantity { get; set; }
    
    /// <summary>
    /// Filtr podle dostupnosti na skladě
    /// </summary>
    public bool? IsInStock { get; set; }
}
```

### InvoiceFilterDto

```csharp
public class InvoiceFilterDto : BaseFilterDto
{
    /// <summary>
    /// Filtr podle čísla faktury
    /// </summary>
    public string? InvoiceNumber { get; set; }
    
    /// <summary>
    /// Filtr podle ID objednávky
    /// </summary>
    public Guid? OrderId { get; set; }
    
    /// <summary>
    /// Filtr podle ID zákazníka
    /// </summary>
    public Guid? CustomerId { get; set; }
    
    /// <summary>
    /// Filtr podle stavu faktury
    /// </summary>
    public InvoiceStatus? Status { get; set; }
    
    /// <summary>
    /// Filtr podle data vystavení (od)
    /// </summary>
    public DateTime? IssueDateFrom { get; set; }
    
    /// <summary>
    /// Filtr podle data vystavení (do)
    /// </summary>
    public DateTime? IssueDateTo { get; set; }
    
    /// <summary>
    /// Filtr podle data splatnosti (od)
    /// </summary>
    public DateTime? DueDateFrom { get; set; }
    
    /// <summary>
    /// Filtr podle data splatnosti (do)
    /// </summary>
    public DateTime? DueDateTo { get; set; }
    
    /// <summary>
    /// Minimální celková částka
    /// </summary>
    public decimal? MinTotalAmount { get; set; }
    
    /// <summary>
    /// Maximální celková částka
    /// </summary>
    public decimal? MaxTotalAmount { get; set; }
    
    /// <summary>
    /// Filtr podle způsobu platby
    /// </summary>
    public PaymentMethod? PaymentMethod { get; set; }
    
    /// <summary>
    /// Pouze zaplacené faktury
    /// </summary>
    public bool? IsPaid { get; set; }
}
```

## 🔧 Implementace konverze

### FilterToSpecificationConverter

```csharp
public static class FilterToSpecificationConverter
{
    public static ISpecification<SampleEntity> ToSpecification(SampleEntityFilterDto filter)
    {
        var spec = new BaseSpecification<SampleEntity>();
        
        // Sestavení kritérií
        Expression<Func<SampleEntity, bool>>? criteria = null;
        
        if (!string.IsNullOrEmpty(filter.NameFilter))
        {
            criteria = CombineWithAnd(criteria, x => x.Name.Contains(filter.NameFilter));
        }
        
        if (filter.IsActive.HasValue)
        {
            criteria = CombineWithAnd(criteria, x => x.IsActive == filter.IsActive.Value);
        }
        
        if (filter.MinAge.HasValue)
        {
            criteria = CombineWithAnd(criteria, x => x.Age >= filter.MinAge.Value);
        }
        
        if (filter.MaxAge.HasValue)
        {
            criteria = CombineWithAnd(criteria, x => x.Age <= filter.MaxAge.Value);
        }
        
        if (filter.CreatedAfter.HasValue)
        {
            criteria = CombineWithAnd(criteria, x => x.CreatedAt > filter.CreatedAfter.Value);
        }
        
        if (filter.CreatedBefore.HasValue)
        {
            criteria = CombineWithAnd(criteria, x => x.CreatedAt < filter.CreatedBefore.Value);
        }
        
        if (!string.IsNullOrEmpty(filter.CreatedBy))
        {
            criteria = CombineWithAnd(criteria, x => x.CreatedBy == filter.CreatedBy);
        }
        
        if (criteria != null)
        {
            spec.SetCriteria(criteria);
        }
        
        // Řazení
        if (!string.IsNullOrEmpty(filter.SortBy))
        {
            var orderExpression = GetOrderExpression<SampleEntity>(filter.SortBy);
            if (orderExpression != null)
            {
                if (filter.SortDescending)
                    spec.AddOrderByDescending(orderExpression);
                else
                    spec.AddOrderBy(orderExpression);
            }
        }
        
        // Stránkování
        if (filter.PageNumber.HasValue && filter.PageSize.HasValue)
        {
            spec.ApplyPagedPaging(filter.PageNumber.Value, filter.PageSize.Value);
        }
        
        return spec;
    }
    
    private static Expression<Func<T, bool>>? CombineWithAnd<T>(
        Expression<Func<T, bool>>? first, 
        Expression<Func<T, bool>> second)
    {
        if (first == null) return second;
        
        var parameter = Expression.Parameter(typeof(T));
        var firstBody = ReplaceParameter(first.Body, first.Parameters[0], parameter);
        var secondBody = ReplaceParameter(second.Body, second.Parameters[0], parameter);
        var combined = Expression.AndAlso(firstBody, secondBody);
        
        return Expression.Lambda<Func<T, bool>>(combined, parameter);
    }
    
    private static Expression ReplaceParameter(Expression expression, ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        return new ParameterReplacer(oldParameter, newParameter).Visit(expression);
    }
    
    private static Expression<Func<T, object>>? GetOrderExpression<T>(string propertyName)
    {
        var parameter = Expression.Parameter(typeof(T));
        var property = typeof(T).GetProperty(propertyName);
        
        if (property == null) return null;
        
        var propertyAccess = Expression.MakeMemberAccess(parameter, property);
        var converted = Expression.Convert(propertyAccess, typeof(object));
        
        return Expression.Lambda<Func<T, object>>(converted, parameter);
    }
}

internal class ParameterReplacer : ExpressionVisitor
{
    private readonly ParameterExpression _oldParameter;
    private readonly ParameterExpression _newParameter;
    
    public ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        _oldParameter = oldParameter;
        _newParameter = newParameter;
    }
    
    protected override Expression VisitParameter(ParameterExpression node)
    {
        return node == _oldParameter ? _newParameter : base.VisitParameter(node);
    }
}
```

## 📝 Příklady použití

### SampleEntity filtrování

```json
POST /v1/samples/query
{
  "nameFilter": "test",
  "isActive": true,
  "minAge": 18,
  "maxAge": 65,
  "createdAfter": "2024-01-01T00:00:00",
  "sortBy": "Name",
  "sortDescending": false,
  "pageNumber": 1,
  "pageSize": 10
}
```

### Order filtrování

```json
POST /v1/orders/query
{
  "customerName": "Novák",
  "status": "Confirmed",
  "orderDateFrom": "2024-01-01T00:00:00",
  "orderDateTo": "2024-12-31T23:59:59",
  "minTotalAmount": 1000.00,
  "currency": "CZK",
  "sortBy": "OrderDate",
  "sortDescending": true,
  "pageNumber": 1,
  "pageSize": 20
}
```

## 🚀 Implementační kroky

1. **Vytvořit DTO třídy** v `Application/Features/Generic/Filters/`
2. **Implementovat konvertery** v `Application/Features/Generic/Converters/`
3. **Upravit endpointy** pro přijímání FilterDto místo ISpecification
4. **Aktualizovat dokumentaci** a Swagger definice
5. **Vytvořit testy** pro nové filtrovací funkcionality
6. **Migrace existujících endpointů** postupně

## ✅ Výhody tohoto přístupu

- ✅ **JSON serializovatelné** - žádné problémy s Expression objekty
- ✅ **Typově bezpečné** - IntelliSense a compile-time kontrola
- ✅ **Snadno testovatelné** - jednoduché JSON payloady
- ✅ **Flexibilní** - snadné přidávání nových filtrů
- ✅ **Dokumentovatelné** - jasné Swagger definice
- ✅ **Uživatelsky přívětivé** - intuitivní API pro frontend vývojáře
