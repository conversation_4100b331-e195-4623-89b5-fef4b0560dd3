using Domain.Entities;
using Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using SharedKernel.Domain;
using Xunit;

namespace Infrastructure.Tests.Persistence;

/// <summary>
/// Testy pro ověření obecné konfigurace entit ze SharedKernel.
/// Testuje automatické nastavení optimistického zam<PERSON>ání a vyloučení DomainEvents.
/// </summary>
public class SharedKernelConfigurationTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly ServiceProvider _serviceProvider;

    public SharedKernelConfigurationTests()
    {
        var services = new ServiceCollection();
        
        // Konfigurace in-memory databáze pro testy
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));
        
        // Mock služby
        services.AddScoped<Application.Abstraction.ICurrentUserService, Infrastructure.Persistence.MockCurrentUserService>();
        services.AddScoped<Application.Services.Events.DomainEventPublisher, Infrastructure.Persistence.MockDomainEventPublisher>();
        
        _serviceProvider = services.BuildServiceProvider();
        _context = _serviceProvider.GetRequiredService<ApplicationDbContext>();
    }

    [Fact]
    public void RowVersion_Should_Be_Configured_For_SharedKernel_Entities()
    {
        // Arrange & Act
        var sampleEntityType = _context.Model.FindEntityType(typeof(SampleEntity));
        
        // Assert
        Assert.NotNull(sampleEntityType);
        
        var rowVersionProperty = sampleEntityType.FindProperty("RowVersion");
        Assert.NotNull(rowVersionProperty);
        Assert.True(rowVersionProperty.IsConcurrencyToken);
        
        // Ověříme, že má nastavenou výchozí hodnotu
        var defaultValue = rowVersionProperty.GetDefaultValue();
        Assert.NotNull(defaultValue);
        Assert.IsType<byte[]>(defaultValue);
    }

    [Fact]
    public void DomainEvents_Should_Be_Ignored_For_SharedKernel_Entities()
    {
        // Arrange & Act
        var sampleEntityType = _context.Model.FindEntityType(typeof(SampleEntity));
        
        // Assert
        Assert.NotNull(sampleEntityType);
        
        // DomainEvents vlastnost by neměla být mapována do databáze
        var domainEventsProperty = sampleEntityType.FindProperty("DomainEvents");
        Assert.Null(domainEventsProperty);
    }

    [Fact]
    public void Entity_Can_Be_Saved_With_RowVersion()
    {
        // Arrange
        var entity = new SampleEntity
        {
            Id = 0, // Bude nastaveno automaticky
            Name = "Test Entity",
            Age = 25,
            Description = "Test Description",
            DateOfBirth = DateTime.Now.AddYears(-25),
            IsActive = true,
            CreatedAt = DateTimeOffset.UtcNow,
            CreatedBy = "test-user"
        };

        // Act
        _context.SampleEntity.Add(entity);
        _context.SaveChanges();

        // Assert
        Assert.True(entity.Id > 0);
        Assert.NotNull(entity.RowVersion);
        Assert.True(entity.RowVersion.Length > 0);
    }

    [Fact]
    public void DomainEvents_Are_Not_Persisted_To_Database()
    {
        // Arrange
        var entity = new SampleEntity
        {
            Id = 0, // Bude nastaveno automaticky
            Name = "Test Entity with Events",
            Age = 30,
            Description = "Test Description with Events",
            DateOfBirth = DateTime.Now.AddYears(-30),
            IsActive = true,
            CreatedAt = DateTimeOffset.UtcNow,
            CreatedBy = "test-user"
        };

        // Přidáme doménovou událost
        entity.AddDomainEvent(new TestDomainEvent("Test Event"));

        // Act
        _context.SampleEntity.Add(entity);

        // Ověříme, že entita má doménovou událost před uložením
        Assert.Single(entity.DomainEvents);

        _context.SaveChanges();

        // Znovu načteme entitu z databáze do nové instance
        var savedEntityId = entity.Id;
        _context.Entry(entity).State = Microsoft.EntityFrameworkCore.EntityState.Detached;
        var reloadedEntity = _context.SampleEntity.Find(savedEntityId);

        // Assert
        // DomainEvents by měly být prázdné u nově načtené entity
        // (protože nejsou mapovány do databáze a nová instance nemá žádné události)
        Assert.NotNull(reloadedEntity);
        Assert.Empty(reloadedEntity.DomainEvents);
    }

    public void Dispose()
    {
        _context?.Dispose();
        _serviceProvider?.Dispose();
    }
}

/// <summary>
/// Testovací doménová událost pro účely testování.
/// </summary>
public class TestDomainEvent : DomainEvent
{
    public string Message { get; }

    public TestDomainEvent(string message)
    {
        Message = message;
    }
}

/// <summary>
/// Mock implementace ICurrentUserService pro testy.
/// </summary>
public class MockCurrentUserService : Application.Abstraction.ICurrentUserService
{
    public string? UserId => "test-user";
    public string? Email => "<EMAIL>";
    public IEnumerable<string> Roles => new[] { "User" };
    public Domain.Identity.UserProfile? Profile => null;
}

/// <summary>
/// Mock implementace DomainEventPublisher pro testy.
/// </summary>
public class MockDomainEventPublisher : Application.Services.Events.DomainEventPublisher
{
    public MockDomainEventPublisher() : base(null!)
    {
    }

    public new async Task Publish(SharedKernel.Domain.DomainEvent domainEvent)
    {
        // Pro testy neděláme nic
        await Task.CompletedTask;
    }
}
