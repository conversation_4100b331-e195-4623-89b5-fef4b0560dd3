# Příklady payloadů pro POST /entities/query endpoint

Tento dokument obsahuje příklady správných payloadů pro univerzální dotazovací endpoint `POST /entities/query`.

## ⚠️ Důležité omezení

**Expression objekty se nedají serializovat do JSON!**

Endpoint `POST /entities/query` očekává v body `ISpecification<TEntity>` objekt, který obsahuje `Expression<Func<T, bool>>` objekty. Tyto objekty se nedají standardně serializovat do JSON, protože obsahují komplexní struktury (Expression Trees), které JSON serializer neumí zpracovat.

## 🔧 Aktuálně funkční přístupy

### 1. Null specifikace (všechny záznamy)

```http
POST /v1/samples/query
Content-Type: application/json

null
```

**Query parametry:**
- `pageNumber` (volitelné) - <PERSON><PERSON><PERSON> str<PERSON> (začíná od 1)
- `pageSize` (volitelné) - velikost stránky
- `useCache` (volitelné) - použít cache (default: false)

### 2. Pouze stránkování bez filtrování

```http
POST /v1/samples/query?pageNumber=1&pageSize=10&useCache=true
Content-Type: application/json

null
```

### 3. Prázdný JSON objekt

```http
POST /v1/samples/query
Content-Type: application/json

{}
```

## 📋 Dostupné entity a jejich endpointy

### SampleEntity
- **Endpoint:** `POST /v1/samples/query`
- **Vlastnosti:** Id, Name, Description, Age, DateOfBirth, IsActive, InternalNotes, CreatedAt, CreatedBy, ModifiedAt, ModifiedBy

### Order (Objednávka)
- **Endpoint:** `POST /v1/orders/query`
- **Vlastnosti:** Id, OrderNumber, OrderDate, CustomerId, CustomerName, CustomerEmail, Status, SubTotal, TaxAmount, DiscountPercentage, DiscountAmount, ShippingCost, TotalAmount, Currency, Notes

### OrderItem (Položka objednávky)
- **Endpoint:** `POST /v1/order-items/query`
- **Vlastnosti:** Id, OrderId, ProductCode, ProductName, ProductDescription, Category, UnitPrice, Quantity, Unit, Weight, TaxRate, DiscountPercentage, LineTotal, LineTaxAmount, LineTotalWithTax, Notes

### Invoice (Faktura)
- **Endpoint:** `POST /v1/invoices/query`
- **Vlastnosti:** Id, InvoiceNumber, IssueDate, DueDate, OrderId, CustomerId, Status, SubTotal, TaxAmount, TotalAmount, PaidAmount, RemainingAmount, Currency, Notes

## 🧪 Příklady testování

### Získání všech SampleEntity záznamů

```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d "null"
```

### Stránkované výsledky (první stránka, 5 záznamů)

```bash
curl -X POST "http://localhost:5000/v1/samples/query?pageNumber=1&pageSize=5" \
  -H "Content-Type: application/json" \
  -d "null"
```

### S použitím cache

```bash
curl -X POST "http://localhost:5000/v1/samples/query?useCache=true" \
  -H "Content-Type: application/json" \
  -d "null"
```

### Testování Order entity

```bash
curl -X POST "http://localhost:5000/v1/orders/query?pageNumber=1&pageSize=10" \
  -H "Content-Type: application/json" \
  -d "null"
```

## 📊 Očekávané odpovědi

### Bez stránkování (všechny záznamy)
```json
[
  {
    "id": 1,
    "name": "Ukázkový záznam",
    "description": "Popis záznamu",
    "age": 25,
    "dateOfBirth": "1998-01-01T00:00:00",
    "isActive": true,
    "createdAt": "2024-01-01T10:00:00",
    "createdBy": "System",
    "modifiedAt": "2024-01-01T10:00:00",
    "modifiedBy": "System"
  }
]
```

### Se stránkováním
```json
{
  "data": [
    {
      "id": 1,
      "name": "Ukázkový záznam",
      "description": "Popis záznamu",
      "isActive": true
    }
  ],
  "pageNumber": 1,
  "pageSize": 10,
  "totalCount": 25,
  "totalPages": 3,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```

## 🔄 Alternativní přístupy pro filtrování

### 1. Použití standardních GET endpointů

```bash
# Všechny záznamy
GET /v1/samples

# Konkrétní záznam podle ID
GET /v1/samples/1

# Stránkované výsledky (pokud je implementováno)
GET /v1/samples?pageNumber=1&pageSize=10
```

### 2. Návrh na vytvoření Filter DTO objektů

Pro budoucí implementaci by bylo vhodné vytvořit jednoduché filter objekty:

```csharp
public class SampleFilterDto
{
    public string? NameFilter { get; set; }
    public bool? IsActive { get; set; }
    public DateTime? CreatedAfter { get; set; }
    public string? SortBy { get; set; }
    public bool SortDescending { get; set; }
}
```

Pak by endpoint mohl vypadat takto:
```http
POST /v1/samples/query
Content-Type: application/json

{
  "nameFilter": "test",
  "isActive": true,
  "createdAfter": "2024-01-01T00:00:00",
  "sortBy": "name",
  "sortDescending": false
}
```

## 🚨 Chybové stavy

### 400 Bad Request
```json
{
  "error": "Neplatná data nebo chyba při deserializaci specifikace"
}
```

### 500 Internal Server Error
```json
{
  "error": "Vnitřní chyba serveru při zpracování dotazu"
}
```

## 📝 Poznámky pro vývojáře

1. **Expression serializace:** Aktuální implementace neumožňuje serializaci Expression objektů do JSON
2. **Alternativní řešení:** Doporučuje se implementovat jednoduché filter DTO objekty
3. **Testování:** Pro testování použijte null payload nebo prázdný JSON objekt
4. **Stránkování:** Funguje správně pomocí query parametrů
5. **Cache:** Lze ovládat pomocí query parametru `useCache`

## 🔍 Debugging

Pro ladění endpointu můžete použít:

1. **Swagger UI:** Dostupné na `/swagger`
2. **Scalar API:** Pokud je nakonfigurováno
3. **Postman/Insomnia:** Pro testování HTTP požadavků
4. **curl:** Pro rychlé testování z příkazové řádky

## 🎯 Doporučení

1. **Krátkodobě:** Použijte null payload pro testování základní funkcionality
2. **Dlouhodobě:** Implementujte jednoduché filter DTO objekty pro skutečné filtrování
3. **Alternativně:** Použijte GET endpointy pro základní operace
4. **Pro komplexní dotazy:** Zvažte implementaci specifických endpointů pro konkrétní use cases

## 📚 Kompletní příklady pro všechny entity

### SampleEntity - Detailní příklady

```bash
# Základní dotaz
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d "null"

# Se stránkováním
curl -X POST "http://localhost:5000/v1/samples/query?pageNumber=2&pageSize=5&useCache=true" \
  -H "Content-Type: application/json" \
  -d "{}"

# Pouze cache
curl -X POST "http://localhost:5000/v1/samples/query?useCache=true" \
  -H "Content-Type: application/json" \
  -d "null"
```

**Očekávaná odpověď (SampleEntity):**
```json
[
  {
    "id": 1,
    "name": "Ukázkový záznam",
    "description": "Popis ukázkového záznamu",
    "age": 25,
    "dateOfBirth": "1998-01-01T00:00:00",
    "isActive": true,
    "internalNotes": "Interní poznámka",
    "createdAt": "2024-01-01T10:00:00",
    "createdBy": "System",
    "modifiedAt": "2024-01-01T10:00:00",
    "modifiedBy": "System"
  }
]
```

### Order - Objednávky

```bash
# Všechny objednávky
curl -X POST "http://localhost:5000/v1/orders/query" \
  -H "Content-Type: application/json" \
  -d "null"

# Stránkované objednávky
curl -X POST "http://localhost:5000/v1/orders/query?pageNumber=1&pageSize=3" \
  -H "Content-Type: application/json" \
  -d "{}"
```

**Očekávaná odpověď (Order):**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "orderNumber": "ORD-2024-001",
    "orderDate": "2024-01-15T10:30:00",
    "customerId": "456e7890-e89b-12d3-a456-************",
    "customerName": "Jan Novák",
    "customerEmail": "<EMAIL>",
    "status": "Confirmed",
    "subTotal": 1500.00,
    "taxAmount": 315.00,
    "discountPercentage": 5.0,
    "discountAmount": 75.00,
    "shippingCost": 100.00,
    "totalAmount": 1840.00,
    "currency": "CZK",
    "notes": "Poznámka k objednávce"
  }
]
```

### OrderItem - Položky objednávek

```bash
# Všechny položky objednávek
curl -X POST "http://localhost:5000/v1/order-items/query" \
  -H "Content-Type: application/json" \
  -d "null"

# S malým stránkováním pro testování
curl -X POST "http://localhost:5000/v1/order-items/query?pageNumber=1&pageSize=2" \
  -H "Content-Type: application/json" \
  -d "{}"
```

**Očekávaná odpověď (OrderItem):**
```json
[
  {
    "id": "789e0123-e89b-12d3-a456-426614174002",
    "orderId": "123e4567-e89b-12d3-a456-************",
    "productCode": "PROD-001",
    "productName": "Testovací produkt",
    "productDescription": "Popis testovacího produktu",
    "category": "Elektronika",
    "unitPrice": 500.00,
    "quantity": 2,
    "unit": "ks",
    "weight": 1.5,
    "taxRate": 21.0,
    "discountPercentage": 0.0,
    "lineTotal": 1000.00,
    "lineTaxAmount": 210.00,
    "lineTotalWithTax": 1210.00,
    "notes": "Poznámka k položce",
    "isInStock": true,
    "expectedDeliveryDate": "2024-01-20T00:00:00"
  }
]
```

### Invoice - Faktury

```bash
# Všechny faktury
curl -X POST "http://localhost:5000/v1/invoices/query" \
  -H "Content-Type: application/json" \
  -d "null"

# Stránkované faktury s cache
curl -X POST "http://localhost:5000/v1/invoices/query?pageNumber=1&pageSize=5&useCache=true" \
  -H "Content-Type: application/json" \
  -d "{}"
```

**Očekávaná odpověď (Invoice):**
```json
[
  {
    "id": "abc1234d-e89b-12d3-a456-************",
    "invoiceNumber": "INV-2024-001",
    "issueDate": "2024-01-16T00:00:00",
    "dueDate": "2024-02-15T00:00:00",
    "orderId": "123e4567-e89b-12d3-a456-************",
    "customerId": "456e7890-e89b-12d3-a456-************",
    "status": "Issued",
    "subTotal": 1500.00,
    "taxAmount": 315.00,
    "totalAmount": 1815.00,
    "paidAmount": 0.00,
    "remainingAmount": 1815.00,
    "currency": "CZK",
    "notes": "Poznámka k faktuře",
    "paymentDate": null,
    "paymentMethod": "BankTransfer",
    "variableSymbol": "********",
    "constantSymbol": "0308",
    "specificSymbol": null
  }
]
```

## 🔧 Testovací skripty

### PowerShell skript pro Windows
```powershell
# Test všech endpointů
$baseUrl = "http://localhost:5000"
$headers = @{ "Content-Type" = "application/json" }

# SampleEntity
Invoke-RestMethod -Uri "$baseUrl/v1/samples/query" -Method POST -Headers $headers -Body "null"

# Orders
Invoke-RestMethod -Uri "$baseUrl/v1/orders/query?pageNumber=1&pageSize=5" -Method POST -Headers $headers -Body "{}"

# OrderItems
Invoke-RestMethod -Uri "$baseUrl/v1/order-items/query" -Method POST -Headers $headers -Body "null"

# Invoices
Invoke-RestMethod -Uri "$baseUrl/v1/invoices/query?useCache=true" -Method POST -Headers $headers -Body "{}"
```

### Bash skript pro Linux/Mac
```bash
#!/bin/bash
BASE_URL="http://localhost:5000"

echo "Testing SampleEntity..."
curl -X POST "$BASE_URL/v1/samples/query" -H "Content-Type: application/json" -d "null"

echo -e "\n\nTesting Orders..."
curl -X POST "$BASE_URL/v1/orders/query?pageNumber=1&pageSize=3" -H "Content-Type: application/json" -d "{}"

echo -e "\n\nTesting OrderItems..."
curl -X POST "$BASE_URL/v1/order-items/query" -H "Content-Type: application/json" -d "null"

echo -e "\n\nTesting Invoices..."
curl -X POST "$BASE_URL/v1/invoices/query?useCache=true" -H "Content-Type: application/json" -d "{}"
```

## 📋 Checklist pro testování

- [ ] **SampleEntity endpoint** - `POST /v1/samples/query`
- [ ] **Order endpoint** - `POST /v1/orders/query`
- [ ] **OrderItem endpoint** - `POST /v1/order-items/query`
- [ ] **Invoice endpoint** - `POST /v1/invoices/query`
- [ ] **Stránkování** - parametry `pageNumber` a `pageSize`
- [ ] **Cache** - parametr `useCache`
- [ ] **Null payload** - test s `null` v body
- [ ] **Prázdný JSON** - test s `{}` v body
- [ ] **Chybové stavy** - test s neplatným JSON
- [ ] **Velké stránky** - test s velkým `pageSize`
- [ ] **Neexistující stránka** - test s vysokým `pageNumber`
