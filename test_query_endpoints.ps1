# PowerShell skript pro testování POST /entities/query endpointů
# Autor: DataCapture API Testing
# Datum: $(Get-Date -Format "yyyy-MM-dd")

param(
    [string]$BaseUrl = "http://localhost:5000",
    [switch]$Verbose = $false
)

# Konfigurace
$headers = @{ 
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

$testResults = @()

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Body = "null",
        [hashtable]$Headers = $headers
    )
    
    Write-Host "🧪 Testování: $Name" -ForegroundColor Cyan
    Write-Host "   URL: $Url" -ForegroundColor Gray
    Write-Host "   Body: $Body" -ForegroundColor Gray
    
    try {
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri $Url -Method POST -Headers $Headers -Body $Body -ErrorAction Stop
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        $result = @{
            Name = $Name
            Url = $Url
            Status = "✅ ÚSPĚCH"
            Duration = "$([math]::Round($duration, 2)) ms"
            RecordCount = if ($response -is [array]) { $response.Count } elseif ($response.data) { $response.data.Count } else { "N/A" }
            Error = $null
        }
        
        Write-Host "   ✅ ÚSPĚCH - $($result.Duration)" -ForegroundColor Green
        if ($result.RecordCount -ne "N/A") {
            Write-Host "   📊 Počet záznamů: $($result.RecordCount)" -ForegroundColor Yellow
        }
        
        if ($Verbose -and $response) {
            Write-Host "   📄 Odpověď (první 500 znaků):" -ForegroundColor Magenta
            $jsonResponse = $response | ConvertTo-Json -Depth 2 -Compress
            $preview = if ($jsonResponse.Length -gt 500) { $jsonResponse.Substring(0, 500) + "..." } else { $jsonResponse }
            Write-Host "   $preview" -ForegroundColor White
        }
        
    } catch {
        $result = @{
            Name = $Name
            Url = $Url
            Status = "❌ CHYBA"
            Duration = "N/A"
            RecordCount = "N/A"
            Error = $_.Exception.Message
        }
        
        Write-Host "   ❌ CHYBA: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    $testResults += $result
    Write-Host ""
    return $result
}

# Hlavička
Write-Host "🚀 DataCapture API - Test POST /entities/query endpointů" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "Base URL: $BaseUrl" -ForegroundColor White
Write-Host "Čas spuštění: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host ""

# Test 1: SampleEntity - základní dotaz
Test-Endpoint -Name "SampleEntity - Všechny záznamy" -Url "$BaseUrl/v1/samples/query"

# Test 2: SampleEntity - se stránkováním
Test-Endpoint -Name "SampleEntity - Stránkování (str. 1, 5 záznamů)" -Url "$BaseUrl/v1/samples/query?pageNumber=1&pageSize=5"

# Test 3: SampleEntity - s cache
Test-Endpoint -Name "SampleEntity - S cache" -Url "$BaseUrl/v1/samples/query?useCache=true"

# Test 4: SampleEntity - prázdný JSON
Test-Endpoint -Name "SampleEntity - Prázdný JSON" -Url "$BaseUrl/v1/samples/query" -Body "{}"

# Test 5: Orders - základní dotaz
Test-Endpoint -Name "Orders - Všechny objednávky" -Url "$BaseUrl/v1/orders/query"

# Test 6: Orders - se stránkováním
Test-Endpoint -Name "Orders - Stránkování (str. 1, 3 záznamy)" -Url "$BaseUrl/v1/orders/query?pageNumber=1&pageSize=3"

# Test 7: OrderItems - základní dotaz
Test-Endpoint -Name "OrderItems - Všechny položky" -Url "$BaseUrl/v1/order-items/query"

# Test 8: OrderItems - malé stránkování
Test-Endpoint -Name "OrderItems - Malé stránkování (str. 1, 2 záznamy)" -Url "$BaseUrl/v1/order-items/query?pageNumber=1&pageSize=2"

# Test 9: Invoices - základní dotaz
Test-Endpoint -Name "Invoices - Všechny faktury" -Url "$BaseUrl/v1/invoices/query"

# Test 10: Invoices - s cache a stránkováním
Test-Endpoint -Name "Invoices - Cache + stránkování" -Url "$BaseUrl/v1/invoices/query?pageNumber=1&pageSize=5&useCache=true"

# Test 11: Neexistující endpoint (pro test chybového stavu)
Test-Endpoint -Name "Neexistující endpoint" -Url "$BaseUrl/v1/nonexistent/query"

# Test 12: Velké stránkování
Test-Endpoint -Name "SampleEntity - Velké stránkování (str. 1, 100 záznamů)" -Url "$BaseUrl/v1/samples/query?pageNumber=1&pageSize=100"

# Shrnutí výsledků
Write-Host "📊 SHRNUTÍ TESTŮ" -ForegroundColor Yellow
Write-Host "=" * 60 -ForegroundColor Yellow

$successCount = ($testResults | Where-Object { $_.Status -eq "✅ ÚSPĚCH" }).Count
$errorCount = ($testResults | Where-Object { $_.Status -eq "❌ CHYBA" }).Count
$totalCount = $testResults.Count

Write-Host "Celkem testů: $totalCount" -ForegroundColor White
Write-Host "Úspěšné: $successCount" -ForegroundColor Green
Write-Host "Chybné: $errorCount" -ForegroundColor Red
Write-Host "Úspěšnost: $([math]::Round(($successCount / $totalCount) * 100, 1))%" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })

Write-Host ""
Write-Host "📋 DETAILNÍ VÝSLEDKY:" -ForegroundColor Yellow
Write-Host "-" * 60 -ForegroundColor Yellow

foreach ($result in $testResults) {
    $statusColor = if ($result.Status -eq "✅ ÚSPĚCH") { "Green" } else { "Red" }
    Write-Host "$($result.Status) $($result.Name)" -ForegroundColor $statusColor
    Write-Host "    URL: $($result.Url)" -ForegroundColor Gray
    Write-Host "    Doba odezvy: $($result.Duration)" -ForegroundColor Gray
    Write-Host "    Počet záznamů: $($result.RecordCount)" -ForegroundColor Gray
    if ($result.Error) {
        Write-Host "    Chyba: $($result.Error)" -ForegroundColor Red
    }
    Write-Host ""
}

# Export výsledků do CSV (volitelné)
if ($testResults.Count -gt 0) {
    $csvPath = "test_results_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
    $testResults | Export-Csv -Path $csvPath -NoTypeInformation -Encoding UTF8
    Write-Host "📄 Výsledky exportovány do: $csvPath" -ForegroundColor Cyan
}

# Doporučení
Write-Host ""
Write-Host "💡 DOPORUČENÍ:" -ForegroundColor Yellow
Write-Host "-" * 60 -ForegroundColor Yellow

if ($errorCount -eq 0) {
    Write-Host "✅ Všechny testy prošly úspěšně!" -ForegroundColor Green
    Write-Host "   Endpointy jsou funkční a připravené k použití." -ForegroundColor White
} else {
    Write-Host "⚠️  Některé testy selhaly:" -ForegroundColor Yellow
    Write-Host "   1. Zkontrolujte, zda je aplikace spuštěná na $BaseUrl" -ForegroundColor White
    Write-Host "   2. Ověřte, zda jsou entity správně registrované" -ForegroundColor White
    Write-Host "   3. Zkontrolujte databázové připojení" -ForegroundColor White
    Write-Host "   4. Podívejte se do logů aplikace pro detaily" -ForegroundColor White
}

Write-Host ""
Write-Host "🔗 DALŠÍ KROKY:" -ForegroundColor Yellow
Write-Host "   • Otevřete Swagger UI: $BaseUrl/swagger" -ForegroundColor White
Write-Host "   • Přečtěte si dokumentaci: API_Query_Endpoint_Examples.md" -ForegroundColor White
Write-Host "   • Pro filtrování implementujte: Filter_DTO_Implementation_Proposal.md" -ForegroundColor White

Write-Host ""
Write-Host "Test dokončen: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
