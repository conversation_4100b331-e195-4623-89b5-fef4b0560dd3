using Application.Abstraction;
using SharedKernel.Abstractions.Mediator;
using SharedKernel.Domain;
using SharedKernel.Models;

namespace Application.Features.Generic.Commands;

// 1.1) Command
public class CreateEntityCommand<TEntity, TEditDto, TKey> : IRequest<Result<TKey>>, IInvalidateCache
    where TEditDto : class, new()
{
    public required TEditDto Payload { get; init; }

    /// <summary>
    /// Cache klíče pro invalidaci cache
    /// </summary>
    public IEnumerable<string> CacheKeys { get; } = Array.Empty<string>();

    /// <summary>
    /// Cache tagy pro invalidaci cache - vymaže všechno pro tuhle entitu
    /// </summary>
    public IEnumerable<string>? CacheTags => new[] { typeof(TEntity).Name };
}


// 1.2) Handler
public class CreateEntityCommandHandler<TEntity, TEditDto, TKey>
    : IRequestHandler<CreateEntityCommand<TEntity,TEditDto, TKey>, Result<TKey>>
    where TEntity : BaseEntity<TKey>
    where TEditDto : class, new()
{
    private readonly IApplicationDbContext _ctx;
    private readonly IUnifiedMapper<TEditDto, TEntity> _mapper;

    public CreateEntityCommandHandler(
        IApplicationDbContext ctx,
        IUnifiedMapper<TEditDto, TEntity> mapper)
    {
        _ctx = ctx;
        _mapper = mapper;
    }

    public async Task<Result<TKey>> Handle(
        CreateEntityCommand<TEntity, TEditDto, TKey> request,
        CancellationToken ct)
    {
        // 1) Map payload to entity
        var entity = _mapper.Map(request.Payload);

        // 2) Add + Save
        await _ctx.Set<TEntity>().AddAsync(entity, ct);
        await _ctx.SaveChangesAsync(ct);

        // 3) Vrať nové id
        return await Result<TKey>.OkAsync(entity.Id);
    }
}
