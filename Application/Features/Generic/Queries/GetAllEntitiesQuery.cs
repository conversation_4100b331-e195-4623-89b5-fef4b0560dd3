using Application.Abstraction;
using SharedKernel.Abstractions.Mediator;
using Application.Exceptions;
using Microsoft.EntityFrameworkCore;
using SharedKernel.Models;

namespace Application.Features.Generic.Queries;

/// <summary>
/// Generický dotaz pro získání všech entit s volitelnou podporou cache a specifikací
/// </summary>
/// <typeparam name="TDto">Typ DTO objektu</typeparam>
/// <typeparam name="TEntity">Typ entity</typeparam>
public class GetAllEntitiesQuery<TDto, TEntity> : IRequest<Result<List<TDto>>>, ICachableQuery<Result<List<TDto>>>
    where TEntity : class
{
    /// <summary>
    /// Název entity pro cache tagy - musí být nastaven handlerem
    /// </summary>
    public string? EntityName { get; set; }

    /// <summary>
    /// Určuje, zda má být použita cache pro tento dotaz
    /// </summary>
    public bool UseCache { get; set; } = false;

    /// <summary>
    /// Specifikace pro filtrování a eager loading
    /// </summary>
    public ISpecification<TEntity>? Specification { get; set; }

    /// <summary>
    /// Klíč pro cache - obsahuje typ DTO a hash specifikace
    /// </summary>
    public string CacheKey
    {
        get
        {
            if (!UseCache)
                return string.Empty;

            var key = $"GetAll_{typeof(TDto).Name}";

            // Pokud je specifikace definována, přidáme její hash do klíče
            if (Specification != null)
            {
                var specHash = GetSpecificationHash(Specification);
                key += $"_Spec{specHash}";
            }

            return key;
        }
    }

    /// <summary>
    /// Tagy pro invalidaci cache
    /// </summary>
    public IEnumerable<string>? Tags => UseCache && !string.IsNullOrEmpty(EntityName) ? new[] { EntityName } : null;

    /// <summary>
    /// Vytvoří hash ze specifikace pro cache klíč
    /// </summary>
    private static string GetSpecificationHash(ISpecification<TEntity> specification)
    {
        // Jednoduchý hash založený na typu specifikace
        // V produkčním prostředí by bylo lepší použít složitější hash zahrnující kritéria
        return specification.GetType().Name.GetHashCode().ToString("X");
    }
}



/// <summary>
/// Handler pro zpracování GetAllEntitiesQuery se specifikací
/// </summary>
public class GetAllEntitiesQueryHandler<TEntity, TDto>
    : GenericCollectionQueryHandler<TEntity, TDto, GetAllEntitiesQuery<TDto, TEntity>>
    where TEntity : class
    where TDto : class, new()
{
    public GetAllEntitiesQueryHandler(IApplicationDbContext context, IUnifiedMapper<TEntity, TDto> mapper)
        : base(context, mapper)
    {

    }

    protected override async Task<List<TDto>> FetchCollectionAsync(GetAllEntitiesQuery<TDto, TEntity> request,
        CancellationToken cancellationToken)
    {
        var query = Context.Set<TEntity>().AsQueryable();

        // Aplikace specifikace pokud je definována
        if (request.Specification != null)
        {
            query = SpecificationEvaluator<TEntity>.GetQuery(query, request.Specification);
        }

        var data = await query.ToListAsync(cancellationToken);
        if (data is null)
            throw new NotFoundException(typeof(TEntity).Name, typeof(TDto).Name);

        // Vracíme přímo kolekci TDto s použitím automatického mapování
        return Mapper.MapCollection(data, useConfig: false).ToList();
    }
}

