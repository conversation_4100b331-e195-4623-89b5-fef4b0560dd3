namespace Application.Features.Sample;

/// <summary>
/// DTO pro čtení dat SampleEntity
/// </summary>
public class SampleDto
{
    /// <summary>
    /// Jedinečný identifikátor entity
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Název vzorku
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Popis vzorku
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Věk
    /// </summary>
    public int? Age { get; set; }

    /// <summary>
    /// Datum narození
    /// </summary>
    public DateTime? DateOfBirth { get; set; }

    /// <summary>
    /// Zda je aktivní
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Interní poznámky
    /// </summary>
    public string? InternalNotes { get; set; }

    /// <summary>
    /// Datum vytvoření
    /// </summary>
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// Kdo vytvořil
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Datum poslední modifikace
    /// </summary>
    public DateTime? ModifiedAt { get; set; }

    /// <summary>
    /// Kdo naposledy modifikoval
    /// </summary>
    public string? ModifiedBy { get; set; }

    /// <summary>
    /// Verze řádku pro optimistické zamykání
    /// </summary>
    public byte[]? RowVersion { get; set; }
}

