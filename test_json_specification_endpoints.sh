#!/bin/bash

# Bash skript pro testování POST /entities/query endpointů s JsonSpecification
# Autor: DataCapture API Testing - JsonSpecification Edition
# Datum: $(date +%Y-%m-%d)

# Konfigurace
BASE_URL="${1:-http://localhost:5000}"
VERBOSE="${2:-false}"

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Proměnné pro statistiky
SUCCESS_COUNT=0
ERROR_COUNT=0
TOTAL_COUNT=0

# Funkce pro testování endpointu
test_endpoint() {
    local name="$1"
    local url="$2"
    local body="$3"
    
    echo -e "${CYAN}🧪 Testování: $name${NC}"
    echo -e "${GRAY}   URL: $url${NC}"
    if [ ${#body} -gt 100 ]; then
        echo -e "${GRAY}   Body: ${body:0:100}...${NC}"
    else
        echo -e "${GRAY}   Body: $body${NC}"
    fi
    
    # Měření času
    start_time=$(date +%s.%3N)
    
    # Provedení požadavku
    response=$(curl -s -w "\n%{http_code}" -X POST "$url" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$body" 2>/dev/null)
    
    end_time=$(date +%s.%3N)
    duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "N/A")
    duration_ms=$(echo "$duration * 1000" | bc -l 2>/dev/null | cut -d. -f1 2>/dev/null || echo "N/A")
    
    # Rozdělení odpovědi a HTTP kódu
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    
    if [ "$http_code" -eq 200 ] 2>/dev/null; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        echo -e "${GREEN}   ✅ ÚSPĚCH - ${duration_ms} ms${NC}"
        
        # Počítání záznamů (pokud je jq dostupné)
        if command -v jq >/dev/null 2>&1; then
            if echo "$response_body" | jq -e 'type == "array"' >/dev/null 2>&1; then
                record_count=$(echo "$response_body" | jq 'length')
                echo -e "${YELLOW}   📊 Počet záznamů: $record_count${NC}"
            elif echo "$response_body" | jq -e '.data | type == "array"' >/dev/null 2>&1; then
                record_count=$(echo "$response_body" | jq '.data | length')
                total_count=$(echo "$response_body" | jq -r '.totalCount // "N/A"')
                echo -e "${YELLOW}   📊 Počet záznamů na stránce: $record_count${NC}"
                echo -e "${YELLOW}   📊 Celkový počet: $total_count${NC}"
            fi
            
            # Verbose výstup
            if [ "$VERBOSE" = "true" ]; then
                echo -e "${BLUE}   📄 Odpověď (první 300 znaků):${NC}"
                echo "$response_body" | jq -c . 2>/dev/null | cut -c1-300 | sed 's/^/   /'
            fi
        else
            echo -e "${YELLOW}   📊 Počet záznamů: N/A (jq není nainstalováno)${NC}"
        fi
    else
        ERROR_COUNT=$((ERROR_COUNT + 1))
        echo -e "${RED}   ❌ CHYBA - HTTP $http_code${NC}"
        if [ -n "$response_body" ]; then
            echo -e "${RED}   Chyba: $(echo "$response_body" | head -c 200)${NC}"
        fi
    fi
    
    echo ""
}

# Hlavička
echo -e "${YELLOW}🚀 DataCapture API - Test JsonSpecification endpointů${NC}"
echo -e "${YELLOW}======================================================================${NC}"
echo -e "${WHITE}Base URL: $BASE_URL${NC}"
echo -e "${WHITE}Čas spuštění: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
echo ""

# Kontrola dostupnosti curl
if ! command -v curl >/dev/null 2>&1; then
    echo -e "${RED}❌ CHYBA: curl není nainstalován${NC}"
    exit 1
fi

# Test 1: SampleEntity - prázdná specifikace
test_endpoint "SampleEntity - Prázdná specifikace" "$BASE_URL/v1/samples/query" "{}"

# Test 2: SampleEntity - pouze aktivní záznamy
test_endpoint "SampleEntity - Pouze aktivní" "$BASE_URL/v1/samples/query" '{
  "propertyFilters": [
    { "propertyName": "IsActive", "value": true }
  ]
}'

# Test 3: SampleEntity - textové vyhledávání
test_endpoint "SampleEntity - Textové vyhledávání" "$BASE_URL/v1/samples/query" '{
  "textSearchFilters": [
    { "propertyName": "Name", "searchText": "test" }
  ],
  "orderByFilter": {
    "propertyName": "Name",
    "descending": false
  }
}'

# Test 4: SampleEntity - rozsah věku
test_endpoint "SampleEntity - Rozsah věku" "$BASE_URL/v1/samples/query" '{
  "rangeFilters": [
    { "propertyName": "Age", "minValue": 18, "maxValue": 65 }
  ],
  "orderByFilter": {
    "propertyName": "Age",
    "descending": false
  }
}'

# Test 5: SampleEntity - komplexní filtr
test_endpoint "SampleEntity - Komplexní filtr" "$BASE_URL/v1/samples/query" '{
  "propertyFilters": [
    { "propertyName": "IsActive", "value": true }
  ],
  "textSearchFilters": [
    { "propertyName": "Name", "searchText": "ukázka" }
  ],
  "rangeFilters": [
    { "propertyName": "Age", "minValue": 20, "maxValue": 50 }
  ],
  "orderByFilter": {
    "propertyName": "CreatedAt",
    "descending": true
  },
  "skip": 0,
  "take": 5,
  "isPagingEnabled": true
}'

# Test 6: SampleEntity - datový rozsah
test_endpoint "SampleEntity - Datový rozsah" "$BASE_URL/v1/samples/query" '{
  "dateRangeFilters": [
    { 
      "propertyName": "CreatedAt",
      "fromDate": "2024-01-01T00:00:00",
      "toDate": "2024-12-31T23:59:59"
    }
  ],
  "orderByFilter": {
    "propertyName": "CreatedAt",
    "descending": true
  }
}'

# Test 7: Orders - filtrování objednávek
test_endpoint "Orders - Filtrování objednávek" "$BASE_URL/v1/orders/query" '{
  "textSearchFilters": [
    { "propertyName": "CustomerName", "searchText": "Novák" }
  ],
  "propertyFilters": [
    { "propertyName": "Currency", "value": "CZK" }
  ],
  "rangeFilters": [
    { "propertyName": "TotalAmount", "minValue": 1000.0 }
  ],
  "orderByFilter": {
    "propertyName": "OrderDate",
    "descending": true
  },
  "skip": 0,
  "take": 10,
  "isPagingEnabled": true
}'

# Test 8: OrderItems - filtrování položek
test_endpoint "OrderItems - Filtrování položek" "$BASE_URL/v1/order-items/query" '{
  "textSearchFilters": [
    { "propertyName": "ProductName", "searchText": "produkt" }
  ],
  "propertyFilters": [
    { "propertyName": "IsInStock", "value": true }
  ],
  "rangeFilters": [
    { "propertyName": "UnitPrice", "minValue": 100.0 },
    { "propertyName": "Quantity", "minValue": 1, "maxValue": 10 }
  ],
  "orderByFilter": {
    "propertyName": "UnitPrice",
    "descending": true
  }
}'

# Test 9: Invoices - filtrování faktur
test_endpoint "Invoices - Filtrování faktur" "$BASE_URL/v1/invoices/query" '{
  "propertyFilters": [
    { "propertyName": "Currency", "value": "CZK" }
  ],
  "dateRangeFilters": [
    { 
      "propertyName": "IssueDate",
      "fromDate": "2024-01-01T00:00:00"
    }
  ],
  "rangeFilters": [
    { "propertyName": "TotalAmount", "minValue": 500.0 }
  ],
  "orderByFilter": {
    "propertyName": "DueDate",
    "descending": false
  },
  "skip": 0,
  "take": 15,
  "isPagingEnabled": true
}'

# Test 10: SampleEntity - pouze stránkování
test_endpoint "SampleEntity - Pouze stránkování" "$BASE_URL/v1/samples/query" '{
  "skip": 0,
  "take": 3,
  "isPagingEnabled": true
}'

# Test 11: SampleEntity - pouze řazení
test_endpoint "SampleEntity - Pouze řazení" "$BASE_URL/v1/samples/query" '{
  "orderByFilter": {
    "propertyName": "Name",
    "descending": true
  }
}'

# Test 12: Chybný filtr (neexistující vlastnost)
test_endpoint "SampleEntity - Neplatná vlastnost" "$BASE_URL/v1/samples/query" '{
  "propertyFilters": [
    { "propertyName": "NeexistujiciVlastnost", "value": "test" }
  ]
}'

# Shrnutí výsledků
echo -e "${YELLOW}📊 SHRNUTÍ TESTŮ${NC}"
echo -e "${YELLOW}======================================================================${NC}"

success_rate=0
if [ $TOTAL_COUNT -gt 0 ]; then
    success_rate=$(echo "scale=1; $SUCCESS_COUNT * 100 / $TOTAL_COUNT" | bc -l 2>/dev/null || echo "N/A")
fi

echo -e "${WHITE}Celkem testů: $TOTAL_COUNT${NC}"
echo -e "${GREEN}Úspěšné: $SUCCESS_COUNT${NC}"
echo -e "${RED}Chybné: $ERROR_COUNT${NC}"
echo -e "${WHITE}Úspěšnost: $success_rate%${NC}"

echo ""
echo -e "${YELLOW}💡 VÝSLEDKY JsonSpecification TESTŮ:${NC}"
echo -e "${YELLOW}----------------------------------------------------------------------${NC}"

if [ $ERROR_COUNT -eq 0 ]; then
    echo -e "${GREEN}🎉 Všechny testy prošly úspěšně!${NC}"
    echo -e "${WHITE}   JsonSpecification funguje správně a umožňuje skutečné filtrování!${NC}"
else
    echo -e "${YELLOW}⚠️  Některé testy selhaly:${NC}"
    echo -e "${WHITE}   1. Zkontrolujte implementaci JsonSpecification${NC}"
    echo -e "${WHITE}   2. Ověřte registraci SpecificationJsonConverter${NC}"
    echo -e "${WHITE}   3. Zkontrolujte názvy vlastností v entitách${NC}"
fi

echo ""
echo -e "${YELLOW}🔗 DALŠÍ INFORMACE:${NC}"
echo -e "${WHITE}   • Dokumentace: API_Query_Endpoint_Working_Examples.md${NC}"
echo -e "${WHITE}   • Swagger UI: $BASE_URL/swagger${NC}"
echo -e "${WHITE}   • JsonSpecification třída: Application/Features/Generic/JsonSpecification.cs${NC}"

echo ""
echo -e "${GRAY}Test dokončen: $(date '+%Y-%m-%d %H:%M:%S')${NC}"

# Návratový kód
if [ $ERROR_COUNT -eq 0 ]; then
    exit 0
else
    exit 1
fi
