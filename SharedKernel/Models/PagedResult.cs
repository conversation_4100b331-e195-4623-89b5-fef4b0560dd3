using System.Linq.Expressions;

namespace SharedKernel.Models;

/// <summary>
/// Stránkovaný výsledek operace, kter<PERSON> rozšiřuje Result pattern o metadata pro stránkování.
/// Kombinuje funkcionalitu Result<List<T>> s informacemi o stránkování.
/// </summary>
/// <typeparam name="T">Typ položek v kolekci</typeparam>
public record PagedResult<T> : Result<List<T>>
{
    /// <summary>
    /// Položky na aktuální stránce.
    /// </summary>
    public List<T> Items => Data ?? new List<T>();

    /// <summary>
    /// Číslo aktuální stránky (začíná od 1).
    /// </summary>
    public int PageNumber { get; }

    /// <summary>
    /// Celkový počet stránek.
    /// </summary>
    public int TotalPages { get; }

    /// <summary>
    /// Celkový počet položek napříč v<PERSON><PERSON>i stránkami.
    /// </summary>
    public int TotalCount { get; }

    /// <summary>
    /// Velikost stránky (počet položek na stránku).
    /// </summary>
    public int PageSize { get; }

    /// <summary>
    /// Indikuje, zda existuje předchozí stránka.
    /// </summary>
    public bool HasPreviousPage => PageNumber > 1;

    /// <summary>
    /// Indikuje, zda existuje následující stránka.
    /// </summary>
    public bool HasNextPage => PageNumber < TotalPages;

    private PagedResult(bool succeeded, List<T> items, int count, int pageNumber, int pageSize, string[] errors)
        : base(succeeded, items, errors)
    {
        TotalCount = count;
        PageSize = pageSize;
        PageNumber = pageNumber;
        TotalPages = pageSize > 0 ? (int)Math.Ceiling(count / (double)pageSize) : 0;
    }

    #region Static Factory Methods

    /// <summary>
    /// Vytvoří úspěšný stránkovaný výsledek.
    /// </summary>
    /// <param name="items">Položky na stránce</param>
    /// <param name="totalCount">Celkový počet položek</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <returns>Úspěšný PagedResult</returns>
    public static PagedResult<T> Ok(List<T> items, int totalCount, int pageNumber, int pageSize)
    {
        ValidatePaging(pageNumber, pageSize);
        return new PagedResult<T>(true, items, totalCount, pageNumber, pageSize, Array.Empty<string>());
    }

    /// <summary>
    /// Vytvoří neúspěšný stránkovaný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Neúspěšný PagedResult</returns>
    public new static PagedResult<T> Error(params string[] errors)
        => new PagedResult<T>(false, new List<T>(), 0, 1, 10, errors);

    /// <summary>
    /// Asynchronně vytvoří úspěšný stránkovaný výsledek.
    /// </summary>
    /// <param name="items">Položky na stránce</param>
    /// <param name="totalCount">Celkový počet položek</param>
    /// <param name="pageNumber">Číslo stránky</param>
    /// <param name="pageSize">Velikost stránky</param>
    /// <returns>Task s úspěšným PagedResult</returns>
    public static Task<PagedResult<T>> OkAsync(List<T> items, int totalCount, int pageNumber, int pageSize)
        => Task.FromResult(Ok(items, totalCount, pageNumber, pageSize));

    /// <summary>
    /// Asynchronně vytvoří neúspěšný stránkovaný výsledek s chybovými zprávami.
    /// </summary>
    /// <param name="errors">Chybové zprávy</param>
    /// <returns>Task s neúspěšným PagedResult</returns>
    public new static Task<PagedResult<T>> ErrorAsync(params string[] errors)
        => Task.FromResult(Error(errors));

    #endregion

    #region Sync Create Methods

    /// <summary>
    /// Synchronní vytvoření stránkovaného výsledku s projekcí v paměti.
    /// </summary>
    public static PagedResult<T> Create<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Func<TSource, T> map)
    {
        try
        {
            ValidatePaging(pageNumber, pageSize);
            // POZOR: EF Core bez OrderBy nemá garantované pořadí
            var totalCount = source.Count();
            var items = source
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList()
                .Select(map)
                .ToList();
            return Ok(items, totalCount, pageNumber, pageSize);
        }
        catch (Exception ex)
        {
            return Error($"Chyba při vytváření stránkovaného výsledku: {ex.Message}");
        }
    }

    /// <summary>
    /// Synchronní vytvoření stránkovaného výsledku s projekcí v SQL.
    /// </summary>
    public static PagedResult<T> Create<TSource>(
        IQueryable<TSource> source,
        int pageNumber,
        int pageSize,
        Expression<Func<TSource, T>> selector)
    {
        try
        {
            ValidatePaging(pageNumber, pageSize);
            // POZOR: EF Core bez OrderBy nemá garantované pořadí
            var totalCount = source.Count();
            var items = source
                .Select(selector)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();
            return Ok(items, totalCount, pageNumber, pageSize);
        }
        catch (Exception ex)
        {
            return Error($"Chyba při vytváření stránkovaného výsledku: {ex.Message}");
        }
    }

    #endregion

    #region Helpers

    private static void ValidatePaging(int pageNumber, int pageSize)
    {
        if (pageNumber < 1)
            throw new ArgumentOutOfRangeException(nameof(pageNumber), "PageNumber must be >= 1.");
        if (pageSize < 1)
            throw new ArgumentOutOfRangeException(nameof(pageSize), "PageSize must be > 0.");
    }

    #endregion
}
