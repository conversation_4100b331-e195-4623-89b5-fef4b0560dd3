# Dokumentace Domain komponent

## Přehled

Domain komponenty v SharedKernel poskytují základní stavební kameny pro implementaci domain vrstvy podle principů Domain-Driven Design (DDD). Tyto komponenty definují základ<PERSON>í rozhraní a abstraktní třídy pro entity, sledování změn, měkk<PERSON> ma<PERSON> a doménové události.

## Architektura

### Umístění v projektu

```
SharedKernel/Domain/
├── IEntity.cs                        # Základní rozhraní pro entity
├── BaseEntity.cs                     # Základní implementace entity
├── BaseTrackableEntity.cs            # Entita s audit poli
├── BaseSoftDeleteEntity.cs           # Entita s měkkým mazáním
├── BaseTrackableSoftDeleteEntity.cs  # Kombinace audit + soft delete
└── DomainEvent.cs                    # Doménové události
```

## Hierarchie rozhraní a tříd

```
IEntity
├── IEntity<T>
│   ├── ITrackableEntity<T>
│   └── ISoftDelete
│
BaseEntity<T> : IEntity<T>
├── BaseTrackableEntity<T> : ITrackableEntity<T>
│   └── BaseTrackableSoftDeleteEntity<T> : ISoftDelete
└── BaseSoftDeleteEntity<T> : ISoftDelete

BaseEntity : IEntity<int>  // Zjednodušená verze pro int ID

DomainEvent
└── Event<T, TEnum> : DomainEvent
```

## Hlavní komponenty

### 1. IEntity rozhraní

#### IEntity
Základní marker rozhraní pro všechny entity v doméně.

```csharp
public interface IEntity
{
}
```

#### IEntity<T>
Rozšířené rozhraní pro entity s identifikátorem a optimistickým zamykáním.

```csharp
public interface IEntity<T> : IEntity
{
    T Id { get; set; }
    
    [Timestamp]
    byte[] RowVersion { get; set; }
}
```

**Vlastnosti:**
- `Id` - Jedinečný identifikátor entity
- `RowVersion` - Verze řádku pro optimistické zamykání

#### ITrackableEntity<T>
Rozhraní pro entity vyžadující sledování změn.

```csharp
public interface ITrackableEntity<T> : IEntity<T>
{
    DateTimeOffset? CreatedAt { get; set; }
    string? CreatedBy { get; set; }
    DateTimeOffset? ModifiedAt { get; set; }
    string? ModifiedBy { get; set; }
}
```

**Vlastnosti:**
- `CreatedAt` - Datum a čas vytvoření
- `CreatedBy` - Identifikátor uživatele, který entitu vytvořil
- `ModifiedAt` - Datum a čas poslední modifikace
- `ModifiedBy` - Identifikátor uživatele, který entitu naposledy modifikoval

#### ISoftDelete
Rozhraní pro entity podporující měkké mazání.

```csharp
public interface ISoftDelete
{
    DateTimeOffset? DeletedAt { get; set; }
    string? DeletedBy { get; set; }
}
```

**Vlastnosti:**
- `DeletedAt` - Datum a čas smazání (null = není smazáno)
- `DeletedBy` - Identifikátor uživatele, který entitu smazal

### 2. BaseEntity třídy

#### BaseEntity (pro int ID)
Zjednodušená základní třída pro entity s int identifikátorem.

```csharp
public abstract class BaseEntity : IEntity<int>
{
    public int Id { get; set; }
    public byte[] RowVersion { get; set; }
}
```

**Použití:**
```csharp
public class Product : BaseEntity
{
    public string Name { get; set; }
    public decimal Price { get; set; }
}
```

#### BaseEntity<T>
Generická základní třída pro entity s libovolným typem identifikátoru.

```csharp
public abstract class BaseEntity<T> : IEntity<T>
{
    public virtual required T Id { get; set; }
    public byte[] RowVersion { get; set; } = new byte[] { 0, 0, 0, 0, 0, 0, 0, 1 };
    
    // Domain Events
    public IReadOnlyCollection<DomainEvent> DomainEvents { get; }
    public void AddDomainEvent(DomainEvent domainEvent);
    public void RemoveDomainEvent(DomainEvent domainEvent);
    public void ClearDomainEvents();
}
```

**Klíčové funkce:**
- **Správa doménových událostí** - Kolekce událostí spojených s entitou
- **Optimistické zamykání** - RowVersion pro SQLite
- **Generický identifikátor** - Podpora int, Guid, string ID

**Použití:**
```csharp
public class User : BaseEntity<Guid>
{
    public string Email { get; set; }
    public string Name { get; set; }
}
```

#### BaseTrackableEntity<T>
Entita s automatickým sledováním změn.

```csharp
public abstract class BaseTrackableEntity<T> : BaseEntity<T>, ITrackableEntity<T>
{
    public DateTimeOffset? CreatedAt { get; set; }
    public string? CreatedBy { get; set; }
    public DateTimeOffset? ModifiedAt { get; set; }
    public string? ModifiedBy { get; set; }
}
```

**Automatické nastavení:**
- `CreatedAt` a `CreatedBy` se nastavují při vytvoření
- `ModifiedAt` a `ModifiedBy` se aktualizují při každé změně
- Používá `DateTimeOffset` pro správnou práci s časovými pásmy

**Použití:**
```csharp
public class Order : BaseTrackableEntity<int>
{
    public string OrderNumber { get; set; }
    public decimal TotalAmount { get; set; }
}
```

#### BaseSoftDeleteEntity<T>
Entita s podporou měkkého mazání.

```csharp
public abstract class BaseSoftDeleteEntity<T> : BaseEntity<T>, ISoftDelete
{
    public DateTimeOffset? DeletedAt { get; set; }
    public string? DeletedBy { get; set; }
}
```

**Funkce měkkého mazání:**
- Entita není fyzicky smazána z databáze
- `DeletedAt` označuje čas smazání
- `DeletedBy` identifikuje uživatele, který entitu smazal
- Smazané entity jsou automaticky filtrovány z dotazů

#### BaseTrackableSoftDeleteEntity<T>
Kombinace sledování změn a měkkého mazání.

```csharp
public abstract class BaseTrackableSoftDeleteEntity<T> : BaseTrackableEntity<T>, ISoftDelete
{
    public DateTimeOffset? DeletedAt { get; set; }
    public string? DeletedBy { get; set; }
}
```

**Poskytuje:**
- Všechny funkce `BaseTrackableEntity`
- Všechny funkce měkkého mazání
- Kompletní audit trail entity

### 3. Doménové události

#### DomainEvent
Abstraktní základní třída pro všechny doménové události.

```csharp
public abstract class DomainEvent
{
    public bool IsPublished { get; private set; }
    public DateTimeOffset DateOccurred { get; protected set; }
    
    public void MarkAsPublished();
}
```

**Vlastnosti:**
- `IsPublished` - Indikuje, zda byla událost publikována
- `DateOccurred` - Čas vzniku události (UTC)
- `MarkAsPublished()` - Označí událost jako zpracovanou

#### Event<T, TEnum>
Generická implementace doménové události.

```csharp
public abstract class Event<T, TEnum>(T entity, TEnum eventType) : DomainEvent
    where TEnum : Enum
{
    public T Entity { get; }
    public TEnum EventType { get; }
}
```

**Parametry:**
- `T` - Typ entity, ke které se událost vztahuje
- `TEnum` - Výčet typů událostí

#### EventType
Základní výčet typů událostí.

```csharp
public enum EventType
{
    Created,    // Entita byla vytvořena
    Updated,    // Entita byla aktualizována
    Deleted     // Entita byla smazána
}
```

## Příklady použití

### Základní entita

```csharp
public class Category : BaseEntity<int>
{
    public string Name { get; set; }
    public string? Description { get; set; }
    
    public Category(string name)
    {
        Name = name;
        // Přidání doménové události
        AddDomainEvent(new CategoryCreatedEvent(this));
    }
}
```

### Sledovatelná entita

```csharp
public class Article : BaseTrackableEntity<Guid>
{
    public string Title { get; set; }
    public string Content { get; set; }
    public bool IsPublished { get; set; }
    
    public void Publish()
    {
        IsPublished = true;
        AddDomainEvent(new ArticlePublishedEvent(this));
    }
}
```

### Entita s měkkým mazáním

```csharp
public class Customer : BaseTrackableSoftDeleteEntity<int>
{
    public string Name { get; set; }
    public string Email { get; set; }
    public List<Order> Orders { get; set; } = new();
    
    public void Deactivate(string userId)
    {
        DeletedAt = DateTimeOffset.UtcNow;
        DeletedBy = userId;
        AddDomainEvent(new CustomerDeactivatedEvent(this));
    }
}
```

### Vlastní doménová událost

```csharp
public class OrderCreatedEvent : Event<Order, OrderEventType>
{
    public OrderCreatedEvent(Order order) : base(order, OrderEventType.Created)
    {
    }
}

public enum OrderEventType
{
    Created,
    Confirmed,
    Shipped,
    Delivered,
    Cancelled
}
```

## Integrace s Entity Framework

### Automatické nastavení audit polí

```csharp
public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
{
    var entries = ChangeTracker.Entries<ITrackableEntity<object>>();
    
    foreach (var entry in entries)
    {
        switch (entry.State)
        {
            case EntityState.Added:
                entry.Entity.CreatedAt = DateTimeOffset.UtcNow;
                entry.Entity.CreatedBy = _currentUserService.UserId;
                break;
                
            case EntityState.Modified:
                entry.Entity.ModifiedAt = DateTimeOffset.UtcNow;
                entry.Entity.ModifiedBy = _currentUserService.UserId;
                break;
        }
    }
    
    return await base.SaveChangesAsync(cancellationToken);
}
```

### Soft Delete filtr

```csharp
protected override void OnModelCreating(ModelBuilder modelBuilder)
{
    // Globální filtr pro soft delete
    foreach (var entityType in modelBuilder.Model.GetEntityTypes())
    {
        if (typeof(ISoftDelete).IsAssignableFrom(entityType.ClrType))
        {
            var parameter = Expression.Parameter(entityType.ClrType, "e");
            var property = Expression.Property(parameter, nameof(ISoftDelete.DeletedAt));
            var condition = Expression.Equal(property, Expression.Constant(null));
            var lambda = Expression.Lambda(condition, parameter);
            
            modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
        }
    }
}
```

### Publikování doménových událostí

```csharp
public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
{
    var domainEvents = ChangeTracker.Entries<BaseEntity<object>>()
        .SelectMany(x => x.Entity.DomainEvents)
        .Where(x => !x.IsPublished)
        .ToList();
    
    var result = await base.SaveChangesAsync(cancellationToken);
    
    // Publikování událostí po úspěšném uložení
    foreach (var domainEvent in domainEvents)
    {
        await _mediator.Publish(new DomainEventNotification<DomainEvent>(domainEvent), cancellationToken);
        domainEvent.MarkAsPublished();
    }
    
    return result;
}
```

## Výhody použití

1. **Konzistence** - Jednotný způsob definice entit napříč aplikací
2. **Audit trail** - Automatické sledování změn bez duplicitního kódu
3. **Soft delete** - Bezpečné "mazání" s možností obnovení
4. **Domain events** - Elegantní způsob reakce na změny v doméně
5. **Optimistické zamykání** - Ochrana před současnými změnami
6. **Časová pásma** - Správná práce s DateTimeOffset
7. **Flexibilita** - Podpora různých typů identifikátorů
8. **Clean Architecture** - Oddělení domain logiky od infrastruktury

## Testování

```csharp
[Test]
public void Should_Add_Domain_Event_When_Entity_Created()
{
    // Arrange
    var product = new Product("Test Product");
    
    // Act
    product.AddDomainEvent(new ProductCreatedEvent(product));
    
    // Assert
    Assert.That(product.DomainEvents.Count, Is.EqualTo(1));
    Assert.That(product.DomainEvents.First(), Is.TypeOf<ProductCreatedEvent>());
}

[Test]
public void Should_Track_Creation_Time()
{
    // Arrange
    var entity = new TestTrackableEntity();
    var beforeCreation = DateTimeOffset.UtcNow;
    
    // Act
    entity.CreatedAt = DateTimeOffset.UtcNow;
    
    // Assert
    Assert.That(entity.CreatedAt, Is.GreaterThanOrEqualTo(beforeCreation));
}
```

## Závěr

Domain komponenty v SharedKernel poskytují robustní základ pro implementaci domain vrstvy s podporou moderních vzorů jako DDD, CQRS a Event Sourcing. Automatizují běžné úkoly jako audit trail a soft delete, zatímco zachovávají flexibilitu pro specifické požadavky aplikace.
