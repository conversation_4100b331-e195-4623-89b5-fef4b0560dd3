using Application.Features.Sample;
using Domain.Entities;
using Infrastructure.Services.Mapper;

// Jed<PERSON><PERSON><PERSON>ý test mapperu
var entity = new SampleEntity
{
    Id = 1,
    Name = "Test",
    Description = "Test Description",
    Age = 25,
    DateOfBirth = DateTime.Now.AddYears(-25),
    IsActive = true,
    InternalNotes = "Test Notes",
    CreatedAt = DateTime.Now,
    CreatedBy = "Test User",
    ModifiedAt = DateTime.Now,
    ModifiedBy = "Test User",
    RowVersion = new byte[] { 1, 2, 3, 4, 5, 6, 7, 8 }
};

var mapper = new UnifiedMapper<SampleEntity, SampleDto>();
var dto = mapper.Map(entity, useConfig: false);

Console.WriteLine($"Entity ID: {entity.Id}");
Console.WriteLine($"DTO ID: {dto.Id}");
Console.WriteLine($"Entity Name: {entity.Name}");
Console.WriteLine($"DTO Name: {dto.Name}");
Console.WriteLine($"Entity Description: {entity.Description}");
Console.WriteLine($"DTO Description: {dto.Description}");
