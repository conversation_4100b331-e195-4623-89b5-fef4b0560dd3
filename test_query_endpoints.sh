#!/bin/bash

# Bash skript pro testování POST /entities/query endpointů
# Autor: DataCapture API Testing
# Datum: $(date +%Y-%m-%d)

# Konfigurace
BASE_URL="${1:-http://localhost:5000}"
VERBOSE="${2:-false}"

# Barvy pro výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
GRAY='\033[0;37m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Proměnné pro statistiky
SUCCESS_COUNT=0
ERROR_COUNT=0
TOTAL_COUNT=0

# Funkce pro testování endpointu
test_endpoint() {
    local name="$1"
    local url="$2"
    local body="${3:-null}"
    
    echo -e "${CYAN}🧪 Testování: $name${NC}"
    echo -e "${GRAY}   URL: $url${NC}"
    echo -e "${GRAY}   Body: $body${NC}"
    
    # Měření času
    start_time=$(date +%s.%3N)
    
    # Provedení požadavku
    response=$(curl -s -w "\n%{http_code}" -X POST "$url" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -d "$body" 2>/dev/null)
    
    end_time=$(date +%s.%3N)
    duration=$(echo "$end_time - $start_time" | bc -l)
    duration_ms=$(echo "$duration * 1000" | bc -l | cut -d. -f1)
    
    # Rozdělení odpovědi a HTTP kódu
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    
    if [ "$http_code" -eq 200 ] 2>/dev/null; then
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        echo -e "${GREEN}   ✅ ÚSPĚCH - ${duration_ms} ms${NC}"
        
        # Počítání záznamů (pokud je to možné)
        if command -v jq >/dev/null 2>&1; then
            if echo "$response_body" | jq -e 'type == "array"' >/dev/null 2>&1; then
                record_count=$(echo "$response_body" | jq 'length')
                echo -e "${YELLOW}   📊 Počet záznamů: $record_count${NC}"
            elif echo "$response_body" | jq -e '.data | type == "array"' >/dev/null 2>&1; then
                record_count=$(echo "$response_body" | jq '.data | length')
                total_count=$(echo "$response_body" | jq -r '.totalCount // "N/A"')
                echo -e "${YELLOW}   📊 Počet záznamů na stránce: $record_count${NC}"
                echo -e "${YELLOW}   📊 Celkový počet: $total_count${NC}"
            fi
            
            # Verbose výstup
            if [ "$VERBOSE" = "true" ]; then
                echo -e "${BLUE}   📄 Odpověď (první 500 znaků):${NC}"
                echo "$response_body" | jq -c . 2>/dev/null | cut -c1-500 | sed 's/^/   /'
            fi
        else
            echo -e "${YELLOW}   📊 Počet záznamů: N/A (jq není nainstalováno)${NC}"
        fi
    else
        ERROR_COUNT=$((ERROR_COUNT + 1))
        echo -e "${RED}   ❌ CHYBA - HTTP $http_code${NC}"
        if [ -n "$response_body" ]; then
            echo -e "${RED}   Chyba: $(echo "$response_body" | head -c 200)${NC}"
        fi
    fi
    
    echo ""
}

# Hlavička
echo -e "${YELLOW}🚀 DataCapture API - Test POST /entities/query endpointů${NC}"
echo -e "${YELLOW}============================================================${NC}"
echo -e "${WHITE}Base URL: $BASE_URL${NC}"
echo -e "${WHITE}Čas spuštění: $(date '+%Y-%m-%d %H:%M:%S')${NC}"
echo ""

# Kontrola dostupnosti curl
if ! command -v curl >/dev/null 2>&1; then
    echo -e "${RED}❌ CHYBA: curl není nainstalován${NC}"
    exit 1
fi

# Kontrola dostupnosti bc pro výpočty
if ! command -v bc >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  VAROVÁNÍ: bc není nainstalován - měření času nebude přesné${NC}"
fi

# Test 1: SampleEntity - základní dotaz
test_endpoint "SampleEntity - Všechny záznamy" "$BASE_URL/v1/samples/query" "null"

# Test 2: SampleEntity - se stránkováním
test_endpoint "SampleEntity - Stránkování (str. 1, 5 záznamů)" "$BASE_URL/v1/samples/query?pageNumber=1&pageSize=5" "null"

# Test 3: SampleEntity - s cache
test_endpoint "SampleEntity - S cache" "$BASE_URL/v1/samples/query?useCache=true" "null"

# Test 4: SampleEntity - prázdný JSON
test_endpoint "SampleEntity - Prázdný JSON" "$BASE_URL/v1/samples/query" "{}"

# Test 5: Orders - základní dotaz
test_endpoint "Orders - Všechny objednávky" "$BASE_URL/v1/orders/query" "null"

# Test 6: Orders - se stránkováním
test_endpoint "Orders - Stránkování (str. 1, 3 záznamy)" "$BASE_URL/v1/orders/query?pageNumber=1&pageSize=3" "null"

# Test 7: OrderItems - základní dotaz
test_endpoint "OrderItems - Všechny položky" "$BASE_URL/v1/order-items/query" "null"

# Test 8: OrderItems - malé stránkování
test_endpoint "OrderItems - Malé stránkování (str. 1, 2 záznamy)" "$BASE_URL/v1/order-items/query?pageNumber=1&pageSize=2" "null"

# Test 9: Invoices - základní dotaz
test_endpoint "Invoices - Všechny faktury" "$BASE_URL/v1/invoices/query" "null"

# Test 10: Invoices - s cache a stránkováním
test_endpoint "Invoices - Cache + stránkování" "$BASE_URL/v1/invoices/query?pageNumber=1&pageSize=5&useCache=true" "null"

# Test 11: Neexistující endpoint (pro test chybového stavu)
test_endpoint "Neexistující endpoint" "$BASE_URL/v1/nonexistent/query" "null"

# Test 12: Velké stránkování
test_endpoint "SampleEntity - Velké stránkování (str. 1, 100 záznamů)" "$BASE_URL/v1/samples/query?pageNumber=1&pageSize=100" "null"

# Shrnutí výsledků
echo -e "${YELLOW}📊 SHRNUTÍ TESTŮ${NC}"
echo -e "${YELLOW}============================================================${NC}"

success_rate=0
if [ $TOTAL_COUNT -gt 0 ]; then
    success_rate=$(echo "scale=1; $SUCCESS_COUNT * 100 / $TOTAL_COUNT" | bc -l 2>/dev/null || echo "N/A")
fi

echo -e "${WHITE}Celkem testů: $TOTAL_COUNT${NC}"
echo -e "${GREEN}Úspěšné: $SUCCESS_COUNT${NC}"
echo -e "${RED}Chybné: $ERROR_COUNT${NC}"
echo -e "${WHITE}Úspěšnost: $success_rate%${NC}"

echo ""
echo -e "${YELLOW}💡 DOPORUČENÍ:${NC}"
echo -e "${YELLOW}------------------------------------------------------------${NC}"

if [ $ERROR_COUNT -eq 0 ]; then
    echo -e "${GREEN}✅ Všechny testy prošly úspěšně!${NC}"
    echo -e "${WHITE}   Endpointy jsou funkční a připravené k použití.${NC}"
else
    echo -e "${YELLOW}⚠️  Některé testy selhaly:${NC}"
    echo -e "${WHITE}   1. Zkontrolujte, zda je aplikace spuštěná na $BASE_URL${NC}"
    echo -e "${WHITE}   2. Ověřte, zda jsou entity správně registrované${NC}"
    echo -e "${WHITE}   3. Zkontrolujte databázové připojení${NC}"
    echo -e "${WHITE}   4. Podívejte se do logů aplikace pro detaily${NC}"
fi

echo ""
echo -e "${YELLOW}🔗 DALŠÍ KROKY:${NC}"
echo -e "${WHITE}   • Otevřete Swagger UI: $BASE_URL/swagger${NC}"
echo -e "${WHITE}   • Přečtěte si dokumentaci: API_Query_Endpoint_Examples.md${NC}"
echo -e "${WHITE}   • Pro filtrování implementujte: Filter_DTO_Implementation_Proposal.md${NC}"

echo ""
echo -e "${GRAY}Test dokončen: $(date '+%Y-%m-%d %H:%M:%S')${NC}"

# Návratový kód
if [ $ERROR_COUNT -eq 0 ]; then
    exit 0
else
    exit 1
fi
