# PowerShell skript pro testování POST /entities/query endpointů s JsonSpecification
# Autor: DataCapture API Testing - JsonSpecification Edition
# Datum: $(Get-Date -Format "yyyy-MM-dd")

param(
    [string]$BaseUrl = "http://localhost:5000",
    [switch]$Verbose = $false
)

# Konfigurace
$headers = @{ 
    "Content-Type" = "application/json"
    "Accept" = "application/json"
}

$testResults = @()

function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Body,
        [hashtable]$Headers = $headers
    )
    
    Write-Host "🧪 Testování: $Name" -ForegroundColor Cyan
    Write-Host "   URL: $Url" -ForegroundColor Gray
    if ($Body.Length -gt 100) {
        Write-Host "   Body: $($Body.Substring(0, 100))..." -ForegroundColor Gray
    } else {
        Write-Host "   Body: $Body" -ForegroundColor Gray
    }
    
    try {
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri $Url -Method POST -Headers $Headers -Body $Body -ErrorAction Stop
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        $result = @{
            Name = $Name
            Url = $Url
            Status = "✅ ÚSPĚCH"
            Duration = "$([math]::Round($duration, 2)) ms"
            RecordCount = if ($response -is [array]) { $response.Count } elseif ($response.data) { $response.data.Count } else { "N/A" }
            Error = $null
        }
        
        Write-Host "   ✅ ÚSPĚCH - $($result.Duration)" -ForegroundColor Green
        if ($result.RecordCount -ne "N/A") {
            Write-Host "   📊 Počet záznamů: $($result.RecordCount)" -ForegroundColor Yellow
        }
        
        if ($Verbose -and $response) {
            Write-Host "   📄 Odpověď (první 300 znaků):" -ForegroundColor Magenta
            $jsonResponse = $response | ConvertTo-Json -Depth 2 -Compress
            $preview = if ($jsonResponse.Length -gt 300) { $jsonResponse.Substring(0, 300) + "..." } else { $jsonResponse }
            Write-Host "   $preview" -ForegroundColor White
        }
        
    } catch {
        $result = @{
            Name = $Name
            Url = $Url
            Status = "❌ CHYBA"
            Duration = "N/A"
            RecordCount = "N/A"
            Error = $_.Exception.Message
        }
        
        Write-Host "   ❌ CHYBA: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    $testResults += $result
    Write-Host ""
    return $result
}

# Hlavička
Write-Host "🚀 DataCapture API - Test JsonSpecification endpointů" -ForegroundColor Yellow
Write-Host "=" * 70 -ForegroundColor Yellow
Write-Host "Base URL: $BaseUrl" -ForegroundColor White
Write-Host "Čas spuštění: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host ""

# Test 1: SampleEntity - prázdná specifikace
Test-Endpoint -Name "SampleEntity - Prázdná specifikace" -Url "$BaseUrl/v1/samples/query" -Body "{}"

# Test 2: SampleEntity - pouze aktivní záznamy
$activeFilter = @{
    propertyFilters = @(
        @{ propertyName = "IsActive"; value = $true }
    )
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Pouze aktivní" -Url "$BaseUrl/v1/samples/query" -Body $activeFilter

# Test 3: SampleEntity - textové vyhledávání
$textSearch = @{
    textSearchFilters = @(
        @{ propertyName = "Name"; searchText = "test" }
    )
    orderByFilter = @{
        propertyName = "Name"
        descending = $false
    }
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Textové vyhledávání" -Url "$BaseUrl/v1/samples/query" -Body $textSearch

# Test 4: SampleEntity - rozsah věku
$ageRange = @{
    rangeFilters = @(
        @{ propertyName = "Age"; minValue = 18; maxValue = 65 }
    )
    orderByFilter = @{
        propertyName = "Age"
        descending = $false
    }
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Rozsah věku" -Url "$BaseUrl/v1/samples/query" -Body $ageRange

# Test 5: SampleEntity - komplexní filtr
$complexFilter = @{
    propertyFilters = @(
        @{ propertyName = "IsActive"; value = $true }
    )
    textSearchFilters = @(
        @{ propertyName = "Name"; searchText = "ukázka" }
    )
    rangeFilters = @(
        @{ propertyName = "Age"; minValue = 20; maxValue = 50 }
    )
    orderByFilter = @{
        propertyName = "CreatedAt"
        descending = $true
    }
    skip = 0
    take = 5
    isPagingEnabled = $true
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Komplexní filtr" -Url "$BaseUrl/v1/samples/query" -Body $complexFilter

# Test 6: SampleEntity - datový rozsah
$dateRange = @{
    dateRangeFilters = @(
        @{ 
            propertyName = "CreatedAt"
            fromDate = "2024-01-01T00:00:00"
            toDate = "2024-12-31T23:59:59"
        }
    )
    orderByFilter = @{
        propertyName = "CreatedAt"
        descending = $true
    }
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Datový rozsah" -Url "$BaseUrl/v1/samples/query" -Body $dateRange

# Test 7: Orders - filtrování objednávek
$orderFilter = @{
    textSearchFilters = @(
        @{ propertyName = "CustomerName"; searchText = "Novák" }
    )
    propertyFilters = @(
        @{ propertyName = "Currency"; value = "CZK" }
    )
    rangeFilters = @(
        @{ propertyName = "TotalAmount"; minValue = 1000.0 }
    )
    orderByFilter = @{
        propertyName = "OrderDate"
        descending = $true
    }
    skip = 0
    take = 10
    isPagingEnabled = $true
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "Orders - Filtrování objednávek" -Url "$BaseUrl/v1/orders/query" -Body $orderFilter

# Test 8: OrderItems - filtrování položek
$itemFilter = @{
    textSearchFilters = @(
        @{ propertyName = "ProductName"; searchText = "produkt" }
    )
    propertyFilters = @(
        @{ propertyName = "IsInStock"; value = $true }
    )
    rangeFilters = @(
        @{ propertyName = "UnitPrice"; minValue = 100.0 }
        @{ propertyName = "Quantity"; minValue = 1; maxValue = 10 }
    )
    orderByFilter = @{
        propertyName = "UnitPrice"
        descending = $true
    }
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "OrderItems - Filtrování položek" -Url "$BaseUrl/v1/order-items/query" -Body $itemFilter

# Test 9: Invoices - filtrování faktur
$invoiceFilter = @{
    propertyFilters = @(
        @{ propertyName = "Currency"; value = "CZK" }
    )
    dateRangeFilters = @(
        @{ 
            propertyName = "IssueDate"
            fromDate = "2024-01-01T00:00:00"
        }
    )
    rangeFilters = @(
        @{ propertyName = "TotalAmount"; minValue = 500.0 }
    )
    orderByFilter = @{
        propertyName = "DueDate"
        descending = $false
    }
    skip = 0
    take = 15
    isPagingEnabled = $true
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "Invoices - Filtrování faktur" -Url "$BaseUrl/v1/invoices/query" -Body $invoiceFilter

# Test 10: SampleEntity - pouze stránkování
$pagingOnly = @{
    skip = 0
    take = 3
    isPagingEnabled = $true
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Pouze stránkování" -Url "$BaseUrl/v1/samples/query" -Body $pagingOnly

# Test 11: SampleEntity - pouze řazení
$sortOnly = @{
    orderByFilter = @{
        propertyName = "Name"
        descending = $true
    }
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Pouze řazení" -Url "$BaseUrl/v1/samples/query" -Body $sortOnly

# Test 12: Chybný filtr (neexistující vlastnost)
$invalidFilter = @{
    propertyFilters = @(
        @{ propertyName = "NeexistujiciVlastnost"; value = "test" }
    )
} | ConvertTo-Json -Depth 3

Test-Endpoint -Name "SampleEntity - Neplatná vlastnost" -Url "$BaseUrl/v1/samples/query" -Body $invalidFilter

# Shrnutí výsledků
Write-Host "📊 SHRNUTÍ TESTŮ" -ForegroundColor Yellow
Write-Host "=" * 70 -ForegroundColor Yellow

$successCount = ($testResults | Where-Object { $_.Status -eq "✅ ÚSPĚCH" }).Count
$errorCount = ($testResults | Where-Object { $_.Status -eq "❌ CHYBA" }).Count
$totalCount = $testResults.Count

Write-Host "Celkem testů: $totalCount" -ForegroundColor White
Write-Host "Úspěšné: $successCount" -ForegroundColor Green
Write-Host "Chybné: $errorCount" -ForegroundColor Red
Write-Host "Úspěšnost: $([math]::Round(($successCount / $totalCount) * 100, 1))%" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })

Write-Host ""
Write-Host "📋 DETAILNÍ VÝSLEDKY:" -ForegroundColor Yellow
Write-Host "-" * 70 -ForegroundColor Yellow

foreach ($result in $testResults) {
    $statusColor = if ($result.Status -eq "✅ ÚSPĚCH") { "Green" } else { "Red" }
    Write-Host "$($result.Status) $($result.Name)" -ForegroundColor $statusColor
    Write-Host "    Doba odezvy: $($result.Duration)" -ForegroundColor Gray
    Write-Host "    Počet záznamů: $($result.RecordCount)" -ForegroundColor Gray
    if ($result.Error) {
        Write-Host "    Chyba: $($result.Error)" -ForegroundColor Red
    }
    Write-Host ""
}

# Export výsledků do CSV
if ($testResults.Count -gt 0) {
    $csvPath = "json_spec_test_results_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
    $testResults | Export-Csv -Path $csvPath -NoTypeInformation -Encoding UTF8
    Write-Host "📄 Výsledky exportovány do: $csvPath" -ForegroundColor Cyan
}

# Doporučení
Write-Host ""
Write-Host "💡 VÝSLEDKY JsonSpecification TESTŮ:" -ForegroundColor Yellow
Write-Host "-" * 70 -ForegroundColor Yellow

if ($errorCount -eq 0) {
    Write-Host "🎉 Všechny testy prošly úspěšně!" -ForegroundColor Green
    Write-Host "   JsonSpecification funguje správně a umožňuje skutečné filtrování!" -ForegroundColor White
} else {
    Write-Host "⚠️  Některé testy selhaly:" -ForegroundColor Yellow
    Write-Host "   1. Zkontrolujte implementaci JsonSpecification" -ForegroundColor White
    Write-Host "   2. Ověřte registraci SpecificationJsonConverter" -ForegroundColor White
    Write-Host "   3. Zkontrolujte názvy vlastností v entitách" -ForegroundColor White
}

Write-Host ""
Write-Host "🔗 DALŠÍ INFORMACE:" -ForegroundColor Yellow
Write-Host "   • Dokumentace: API_Query_Endpoint_Working_Examples.md" -ForegroundColor White
Write-Host "   • Swagger UI: $BaseUrl/swagger" -ForegroundColor White
Write-Host "   • JsonSpecification třída: Application/Features/Generic/JsonSpecification.cs" -ForegroundColor White

Write-Host ""
Write-Host "Test dokončen: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Gray
