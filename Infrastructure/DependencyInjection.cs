﻿﻿﻿﻿﻿using Application.Abstraction;
using Application.Services.Events;
using Application.Features.Sample;
using Application.Features.Orders;
using Application.Features.Invoices;
using Domain.Entities;
using Domain.System;
using Infrastructure.Identity;
using Infrastructure.Persistence;
using SharedKernel.Abstractions.Mediator;
using SharedKernel.Infrastructure.Mediator;
using Infrastructure.Persistence.Interceptors;
using Infrastructure.RuleEngine;
using Infrastructure.Services;
using Infrastructure.Services.Mapper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Builder;
using Infrastructure.RuleEngine.API;
using Infrastructure.RuleEngine.Services;
using Infrastructure.Services.Identity;
using Infrastructure.Diagnostics;
using Microsoft.Identity.Web;
using System.Reflection;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection") ??
                               throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

        // Registrace interceptorů
        services.AddScoped<TrackableEntityInterceptor>();
        services.AddScoped<AuditableEntityInterceptor>();

        services.AddDbContext<ApplicationDbContext>((serviceProvider, options) =>
        {
            var trackableInterceptor = serviceProvider.GetRequiredService<TrackableEntityInterceptor>();
            var auditableInterceptor = serviceProvider.GetRequiredService<AuditableEntityInterceptor>();

            options.UseSqlite(connectionString)
                   .AddInterceptors(trackableInterceptor, auditableInterceptor);
        });

        // TODO tohle proveřit v GetPagedSamplesQueryHandler
        // Registruje ApplicationDbContext jako IApplicationDbContext
        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

        // Registrace Mediator služeb (nyní ze SharedKernel)
        services.AddScoped<IMediator, SharedKernel.Infrastructure.Mediator.Mediator>();
        services.AddScoped<INotificationPublisher, SharedKernel.Infrastructure.Mediator.ParallelNotificationPublisher>();
        

        services.AddIdentity<ApplicationUser, IdentityRole<int>>()
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders();
        services.AddAuthentication()
            .AddMicrosoftIdentityWebApp(configuration.GetSection("AzureAd"));
        
        // Registrace služeb pro entity
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddScoped<DomainEventPublisher>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddScoped<ISessionInfoService, SessionInfoService>();

        services.AddMemoryCache();
        services.AddScoped<ICacheService, MemoryCacheService>();
        services.AddScoped<Application.Abstraction.ICacheKeyProvider, CacheKeyProvider>();

        services.AddHttpContextAccessor();
        services.AddScoped<ICurrentUserService, CurrentUserService>();
        services.AddAuthorization(options =>
        {
            // definuj policy podle rolí
            options.AddPolicy("RequireAdmin", p => p.RequireRole("Admin"));
        });

        // Registrace mapperů
        RegisterMappers(services);

        // Registrace RuleEngine komponent
        RegisterRuleEngine(services);

        return services;
    }

    /// <summary>
    /// Registruje mappery pro entity a DTO automaticky pro všechny registrované entity
    /// </summary>
    private static void RegisterMappers(IServiceCollection services)
    {
        // Získáme informace o všech entitách z Application vrstvy
        var entityTypes = Application.DependencyInjection.GetEntityTypesForInfrastructure();

        foreach (var entityInfo in entityTypes)
        {
            RegisterMappersForEntity(services, entityInfo);
        }
    }

    /// <summary>
    /// Registruje mappery pro konkrétní entitu
    /// </summary>
    /// <param name="services">Kolekce služeb</param>
    /// <param name="entityInfo">Informace o entitě</param>
    private static void RegisterMappersForEntity(IServiceCollection services, object entityInfo)
    {
        // Použijeme reflection pro získání vlastností z objektu
        var entityInfoType = entityInfo.GetType();
        var entityType = (Type)entityInfoType.GetProperty("EntityType")!.GetValue(entityInfo)!;
        var dtoType = (Type)entityInfoType.GetProperty("DtoType")!.GetValue(entityInfo)!;
        var addEditType = (Type)entityInfoType.GetProperty("AddEditType")!.GetValue(entityInfo)!;

        // Použijeme reflection pro volání generické metody registrace
        var registerMethod = typeof(DependencyInjection)
            .GetMethod(nameof(RegisterMappersForEntityGeneric), BindingFlags.NonPublic | BindingFlags.Static)!
            .MakeGenericMethod(entityType, dtoType, addEditType);

        registerMethod.Invoke(null, new object[] { services });
    }

    /// <summary>
    /// Generická metoda pro registraci mapperů pro konkrétní typy
    /// </summary>
    /// <typeparam name="TEntity">Typ entity</typeparam>
    /// <typeparam name="TDto">Typ DTO</typeparam>
    /// <typeparam name="TAddEdit">Typ AddEdit DTO</typeparam>
    /// <param name="services">Kolekce služeb</param>
    private static void RegisterMappersForEntityGeneric<TEntity, TDto, TAddEdit>(IServiceCollection services)
        where TEntity : class
        where TDto : class
        where TAddEdit : class
    {
        // Registrace Entity -> DTO mapper
        services.AddSingleton<IUnifiedMapper<TEntity, TDto>>(_ => new UnifiedMapper<TEntity, TDto>());

        // Registrace AddEdit -> Entity mapper
        services.AddSingleton<IUnifiedMapper<TAddEdit, TEntity>>(_ => new UnifiedMapper<TAddEdit, TEntity>());
    }

    /// <summary>
    /// Registruje komponenty RuleEngine systému
    /// </summary>
    private static void RegisterRuleEngine(IServiceCollection services)
    {
        // Registrace základních komponent
        services.AddScoped<IRuleRepository, RuleRepository>();
        services.AddScoped<IRuleDataProvider, RuleDataProvider>();

        // Registrace entity type map pro RuleEngine - automaticky pro všechny registrované entity
        services.AddSingleton<IReadOnlyDictionary<string, Type>>(provider =>
        {
            var entityTypeMap = new Dictionary<string, Type>();

            // Automatická registrace všech entit z Application vrstvy
            var entityTypes = Application.DependencyInjection.GetEntityTypesForInfrastructure();
            foreach (var entityInfo in entityTypes)
            {
                // Použijeme reflection pro získání vlastností z objektu
                var entityInfoType = entityInfo.GetType();
                var entityType = (Type)entityInfoType.GetProperty("EntityType")!.GetValue(entityInfo)!;

                // Použijeme název entity bez "Entity" suffixu jako klíč
                var entityName = entityType.Name;
                entityTypeMap[entityName] = entityType;
            }

            // Registrace systémových entit, které nejsou v hlavní registraci
            entityTypeMap["BusinessRule"] = typeof(BusinessRule);
            entityTypeMap["AuditTrail"] = typeof(AuditTrail);
            entityTypeMap["SystemLog"] = typeof(SystemLog);

            return entityTypeMap;
        });

        // Registrace ExpressionBuilder
        services.AddScoped<IExpressionBuilder, ExpressionBuilder>();

        // Registrace EntityMetadataService pro dynamické generování metadat
        services.AddScoped<IEntityMetadataService, EntityMetadataService>();

        // Registrace CalculationEngine s vylepšenou funkcionalitou a loggingem
        services.AddScoped<CalculationEngine>();
    }

    /// <summary>
    /// Registruje RuleEngine endpointy do aplikace
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication UseRuleEngineEndpoints(this WebApplication app)
    {
        app.MapRuleEngineEndpoints();
        return app;
    }

    /// <summary>
    /// Registruje diagnostické endpointy do aplikace
    /// </summary>
    /// <param name="app">WebApplication instance</param>
    /// <returns>WebApplication pro fluent API</returns>
    public static WebApplication UseDiagnosticEndpoints(this WebApplication app)
    {
        app.MapEntityDiscoveryDiagnostics();
        return app;
    }
}