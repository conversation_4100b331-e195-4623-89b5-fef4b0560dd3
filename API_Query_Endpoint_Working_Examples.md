# 🎉 Funkční příklady payloadů pro POST /entities/query endpoint

Tento dokument obsahuje **skutečně <PERSON>** příklady payloadů pro univerzální dotazovací endpoint `POST /entities/query` po implementaci `JsonSpecification<T>` a `SpecificationJsonConverter`.

## ✅ Řešení problému s Expression serializací

Implementovali jsme `JsonSpecification<T>` třídu a custom JSON converter, který umožňuje:
- ✅ **Deserializaci JSON payloadu** do `ISpecification<T>`
- ✅ **Skutečné filtrování** bez Expression objektů v JSON
- ✅ **Řazení a stránkování** přímo z JSON
- ✅ **Eager loading** pomocí názvů vlastností

## 🔧 Dostupné typy filtrů

### 1. PropertyFilters - Filtrování podle rovnosti
```json
{
  "propertyFilters": [
    { "propertyName": "IsActive", "value": true },
    { "propertyName": "Age", "value": 25 }
  ]
}
```

### 2. RangeFilters - Filtrování podle roz<PERSON>
```json
{
  "rangeFilters": [
    { "propertyName": "Age", "minValue": 18, "maxValue": 65 },
    { "propertyName": "Price", "minValue": 100.0 }
  ]
}
```

### 3. TextSearchFilters - Textové vyhledávání (Contains)
```json
{
  "textSearchFilters": [
    { "propertyName": "Name", "searchText": "test" },
    { "propertyName": "Description", "searchText": "ukázka" }
  ]
}
```

### 4. DateRangeFilters - Filtrování podle data
```json
{
  "dateRangeFilters": [
    { 
      "propertyName": "CreatedAt", 
      "fromDate": "2024-01-01T00:00:00",
      "toDate": "2024-12-31T23:59:59"
    }
  ]
}
```

### 5. OrderByFilter - Řazení
```json
{
  "orderByFilter": {
    "propertyName": "Name",
    "descending": false
  }
}
```

### 6. IncludeProperties - Eager loading
```json
{
  "includeProperties": ["Items", "Customer"]
}
```

### 7. Stránkování
```json
{
  "skip": 0,
  "take": 10,
  "isPagingEnabled": true
}
```

## 📋 Kompletní příklady pro jednotlivé entity

### SampleEntity - Základní filtrování

```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyFilters": [
      { "propertyName": "IsActive", "value": true }
    ],
    "orderByFilter": {
      "propertyName": "Name",
      "descending": false
    }
  }'
```

### SampleEntity - Textové vyhledávání

```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "textSearchFilters": [
      { "propertyName": "Name", "searchText": "test" }
    ],
    "rangeFilters": [
      { "propertyName": "Age", "minValue": 18, "maxValue": 65 }
    ],
    "orderByFilter": {
      "propertyName": "CreatedAt",
      "descending": true
    },
    "skip": 0,
    "take": 5,
    "isPagingEnabled": true
  }'
```

### SampleEntity - Komplexní filtrování

```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyFilters": [
      { "propertyName": "IsActive", "value": true }
    ],
    "textSearchFilters": [
      { "propertyName": "Name", "searchText": "ukázka" }
    ],
    "dateRangeFilters": [
      { 
        "propertyName": "CreatedAt", 
        "fromDate": "2024-01-01T00:00:00"
      }
    ],
    "rangeFilters": [
      { "propertyName": "Age", "minValue": 20, "maxValue": 50 }
    ],
    "orderByFilter": {
      "propertyName": "Name",
      "descending": false
    },
    "skip": 0,
    "take": 10,
    "isPagingEnabled": true
  }'
```

### Order - Filtrování objednávek

```bash
curl -X POST "http://localhost:5000/v1/orders/query" \
  -H "Content-Type: application/json" \
  -d '{
    "textSearchFilters": [
      { "propertyName": "CustomerName", "searchText": "Novák" }
    ],
    "propertyFilters": [
      { "propertyName": "Status", "value": "Confirmed" }
    ],
    "rangeFilters": [
      { "propertyName": "TotalAmount", "minValue": 1000.0, "maxValue": 10000.0 }
    ],
    "dateRangeFilters": [
      { 
        "propertyName": "OrderDate", 
        "fromDate": "2024-01-01T00:00:00",
        "toDate": "2024-12-31T23:59:59"
      }
    ],
    "orderByFilter": {
      "propertyName": "OrderDate",
      "descending": true
    },
    "includeProperties": ["Items"],
    "skip": 0,
    "take": 20,
    "isPagingEnabled": true
  }'
```

### OrderItem - Filtrování položek objednávek

```bash
curl -X POST "http://localhost:5000/v1/order-items/query" \
  -H "Content-Type: application/json" \
  -d '{
    "textSearchFilters": [
      { "propertyName": "ProductName", "searchText": "produkt" },
      { "propertyName": "Category", "searchText": "elektronika" }
    ],
    "rangeFilters": [
      { "propertyName": "UnitPrice", "minValue": 100.0 },
      { "propertyName": "Quantity", "minValue": 1, "maxValue": 10 }
    ],
    "propertyFilters": [
      { "propertyName": "IsInStock", "value": true }
    ],
    "orderByFilter": {
      "propertyName": "UnitPrice",
      "descending": true
    },
    "includeProperties": ["Order"],
    "skip": 0,
    "take": 15,
    "isPagingEnabled": true
  }'
```

### Invoice - Filtrování faktur

```bash
curl -X POST "https://localhost:7003/v1/invoices/query" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyFilters": [
      { "propertyName": "Status", "value": "Issued" },
      { "propertyName": "Currency", "value": "CZK" }
    ],
    "dateRangeFilters": [
      { 
        "propertyName": "IssueDate", 
        "fromDate": "2024-01-01T00:00:00",
        "toDate": "2024-03-31T23:59:59"
      },
      { 
        "propertyName": "DueDate", 
        "toDate": "2024-12-31T23:59:59"
      }
    ],
    "rangeFilters": [
      { "propertyName": "TotalAmount", "minValue": 500.0 },
      { "propertyName": "RemainingAmount", "minValue": 0.01 }
    ],
    "orderByFilter": {
      "propertyName": "DueDate",
      "descending": false
    },
    "includeProperties": ["Order"],
    "skip": 0,
    "take": 25,
    "isPagingEnabled": true
  }'
```

## 🧪 Jednoduché příklady pro rychlé testování

### Pouze aktivní záznamy
```json
{
  "propertyFilters": [
    { "propertyName": "IsActive", "value": true }
  ]
}
```

### Textové vyhledávání
```json
{
  "textSearchFilters": [
    { "propertyName": "Name", "searchText": "test" }
  ]
}
```

### Stránkování bez filtrů
```json
{
  "skip": 0,
  "take": 5,
  "isPagingEnabled": true
}
```

### Řazení podle názvu
```json
{
  "orderByFilter": {
    "propertyName": "Name",
    "descending": false
  }
}
```

### Rozsah věku
```json
{
  "rangeFilters": [
    { "propertyName": "Age", "minValue": 18, "maxValue": 65 }
  ]
}
```

## 📊 Očekávané odpovědi

### Bez stránkování
```json
[
  {
    "id": 1,
    "name": "Filtrovaný záznam",
    "isActive": true,
    "age": 25,
    "createdAt": "2024-01-15T10:30:00"
  }
]
```

### Se stránkováním
```json
{
  "data": [
    {
      "id": 1,
      "name": "Filtrovaný záznam",
      "isActive": true
    }
  ],
  "pageNumber": 1,
  "pageSize": 10,
  "totalCount": 3,
  "totalPages": 1,
  "hasPreviousPage": false,
  "hasNextPage": false
}
```

## 🚨 Chybové stavy

### Neplatný název vlastnosti
```json
{
  "error": "Vlastnost 'NeexistujiciVlastnost' nebyla nalezena na typu 'SampleEntity'"
}
```

### Neplatný typ hodnoty
```json
{
  "error": "Nelze převést hodnotu 'text' na typ 'Int32' pro vlastnost 'Age'"
}
```

## 💡 Tipy pro použití

1. **Kombinace filtrů**: Všechny filtry se kombinují pomocí AND operátoru
2. **Case-insensitive**: Názvy vlastností jsou case-insensitive
3. **Null hodnoty**: Prázdné filtry se ignorují
4. **Stránkování**: Nastavte `isPagingEnabled: true` pro aktivaci stránkování
5. **Eager loading**: Použijte `includeProperties` pro načítání souvisejících entit

## 🔄 Zpětná kompatibilita

Stále fungují i původní přístupy:
- `null` payload (všechny záznamy)
- `{}` prázdný JSON objekt
- Query parametry pro stránkování a cache

## 🎯 Výhody nového řešení

✅ **Skutečné filtrování** - Žádné omezení Expression objektů  
✅ **JSON serializovatelné** - Standardní JSON bez speciálních konverterů  
✅ **Typově bezpečné** - Kontrola názvů vlastností za běhu  
✅ **Flexibilní** - Snadné kombinování různých typů filtrů  
✅ **Dokumentovatelné** - Jasné Swagger definice  
✅ **Testovatelné** - Jednoduché JSON payloady pro testování
