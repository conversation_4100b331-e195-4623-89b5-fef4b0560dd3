# 🚀 Rychlý test JsonSpecification funkcionality

Tento dokument obsahuje nejjednod<PERSON><PERSON><PERSON><PERSON>, jak rychle otestovat novou JsonSpecification funkcionalitu.

## ⚡ Nejrychlej<PERSON><PERSON> test

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> specif<PERSON> (všechny záznamy)
```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d "{}"
```

### 2. Pouze aktivní z<PERSON>znamy
```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyFilters": [
      { "propertyName": "IsActive", "value": true }
    ]
  }'
```

### 3. Textové vyhledávání
```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "textSearchFilters": [
      { "propertyName": "Name", "searchText": "test" }
    ]
  }'
```

### 4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (první 3 záznamy)
```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "skip": 0,
    "take": 3,
    "isPagingEnabled": true
  }'
```

### 5. Řazení podle názvu (sestupně)
```bash
curl -X POST "http://localhost:5000/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "orderByFilter": {
      "propertyName": "Name",
      "descending": true
    }
  }'
```

## 🧪 Automatické testování

### PowerShell (Windows)
```powershell
.\test_json_specification_endpoints.ps1
```

### Bash (Linux/Mac)
```bash
./test_json_specification_endpoints.sh
```

## 📊 Co očekávat

### ✅ Úspěšná odpověď (bez stránkování)
```json
[
  {
    "id": 1,
    "name": "Ukázkový záznam",
    "description": "Popis záznamu",
    "age": 25,
    "isActive": true,
    "createdAt": "2024-01-01T10:00:00"
  }
]
```

### ✅ Úspěšná odpověď (se stránkováním)
```json
{
  "data": [
    {
      "id": 1,
      "name": "Ukázkový záznam",
      "isActive": true
    }
  ],
  "pageNumber": 1,
  "pageSize": 3,
  "totalCount": 10,
  "totalPages": 4,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```

### ❌ Chybová odpověď (neplatná vlastnost)
```json
{
  "error": "Vlastnost 'NeexistujiciVlastnost' nebyla nalezena na typu 'SampleEntity'"
}
```

## 🔍 Kontrola funkcionality

Pokud JsonSpecification funguje správně, měli byste vidět:

1. **HTTP 200** odpovědi pro všechny platné požadavky
2. **Filtrované výsledky** podle zadaných kritérií
3. **Správné řazení** podle OrderByFilter
4. **Stránkované výsledky** s metadaty (totalCount, totalPages, atd.)
5. **Kombinované filtry** - všechny filtry se aplikují současně

## 🚨 Možné problémy

### Problém: HTTP 500 chyba
**Řešení:** Zkontrolujte, zda je `SpecificationJsonConverter` správně registrován v `API/DependencyInjection.cs`

### Problém: Filtry nefungují
**Řešení:** Ověřte názvy vlastností v entitách (case-insensitive, ale musí existovat)

### Problém: Deserializace selhává
**Řešení:** Zkontrolujte JSON syntax a ujistěte se, že hodnoty odpovídají typům vlastností

## 📝 Poznámky

- **Case-insensitive**: Názvy vlastností jsou case-insensitive (`"name"` = `"Name"`)
- **Kombinace filtrů**: Všechny filtry se kombinují pomocí AND operátoru
- **Null hodnoty**: Prázdné nebo null filtry se ignorují
- **Typ konverze**: Hodnoty se automaticky konvertují na správný typ vlastnosti

## 🎯 Další kroky

Po úspěšném testu základní funkcionality můžete:

1. **Otestovat komplexnější filtry** - kombinace více typů filtrů
2. **Vyzkoušet různé entity** - Orders, OrderItems, Invoices
3. **Testovat chybové stavy** - neplatné vlastnosti, špatné typy hodnot
4. **Integrovat do frontendu** - použít JSON payloady ve vaší aplikaci

## 🔗 Další dokumentace

- **Kompletní příklady**: `API_Query_Endpoint_Working_Examples.md`
- **Implementace**: `Application/Features/Generic/JsonSpecification.cs`
- **JSON Converter**: `Infrastructure/Conversions/SpecificationJsonConverter.cs`
- **Testovací skripty**: `test_json_specification_endpoints.*`
