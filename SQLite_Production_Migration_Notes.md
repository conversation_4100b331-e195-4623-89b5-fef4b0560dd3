# 📋 SQLite → MS SQL Server Migration Notes

Tento dokument obsahuje seznam všech úprav v kódu, kter<PERSON> byly provedeny kvůli omezením SQLite a které **NEBUDOU POTŘEBA** v produkci s MS SQL Server.

## 🎯 Účel dokumentu

- **Vývoj:** SQLite pro jednoduchost a rychlost vývoje
- **Produkce:** MS SQL Server pro plnou funkcionalnost a výkon
- **Migrace:** Tento dokument pomůže identifikovat a odstranit SQLite-specifické úpravy

## 🔍 SQLite specifické úpravy v kódu

### 1. JsonSpecification.cs - Decimal řazení

**Soubor:** `Application/Features/Generic/JsonSpecification.cs`  
**Metoda:** `BuildOrderByExpression()`  
**Řádky:** ~434-441

**Problém SQLite:**
```
SQLite does not support expressions of type 'decimal' in ORDER BY clauses.
```

**Sou<PERSON><PERSON><PERSON> úprava:**
```csharp
// TODO: PRODUKCE - Odstranit po přechodu na MS SQL Server
// POZNÁMKA: SQLite nepodporuje řazení podle decimal typů v ORDER BY klauzulích
// MS SQL Server toto omezení nemá, takže v produkci nebude tato konverze potřeba
if (property.PropertyType == typeof(decimal) || property.PropertyType == typeof(decimal?))
{
    // Pro SQLite konvertujeme decimal na double
    // V MS SQL Server lze řadit přímo podle decimal bez konverze
    var convertToDouble = Expression.Convert(propertyAccess, typeof(double));
    var convertToObject = Expression.Convert(convertToDouble, typeof(object));
    return Expression.Lambda<Func<T, object>>(convertToObject, parameter);
}
```

**Produkční verze (MS SQL Server):**
```csharp
// Přímé řazení podle decimal - funguje v MS SQL Server
var converted = Expression.Convert(propertyAccess, typeof(object));
return Expression.Lambda<Func<T, object>>(converted, parameter);
```

**Akce při migraci:**
- [ ] Odstranit if podmínku pro decimal typy
- [ ] Použít standardní konverzi na object pro všechny typy
- [ ] Otestovat řazení podle decimal polí v MS SQL Server

## ✅ Úpravy NEZÁVISLÉ na databázi

Tyto úpravy jsou obecné a budou potřeba i v MS SQL Server:

### 1. Include operace (EF Core omezení)

**Soubor:** `Application/Features/Generic/JsonSpecification.cs`  
**Metoda:** `BuildExpressions()`  
**Důvod:** Složitost EF Core Include expressions (nezávisle na databázi)

**Status:** Dočasně vypnuté, vyžaduje implementaci

### 2. JSON deserializace (System.Text.Json)

**Soubor:** `Application/Features/Generic/JsonSpecification.cs`  
**Metody:** `ConvertJsonValue()`, `ConvertJsonElement()`  
**Důvod:** Ošetření JsonElement objektů z System.Text.Json

**Status:** Potřebné i v produkci

### 3. Case-insensitive vyhledávání vlastností

**Soubor:** `Application/Features/Generic/JsonSpecification.cs`  
**Všechny Build* metody**  
**Důvod:** Uživatelská přívětivost API

**Status:** Potřebné i v produkci

## 🚀 Migrace na MS SQL Server - Checklist

### Před migrací
- [ ] Identifikovat všechny SQLite specifické úpravy
- [ ] Připravit MS SQL Server connection string
- [ ] Vytvořit MS SQL Server databázi

### Během migrace
- [ ] **Odstranit decimal konverzi** v `BuildOrderByExpression()`
- [ ] Změnit connection string na MS SQL Server
- [ ] Spustit migrace pro MS SQL Server
- [ ] Aktualizovat `appsettings.json` pro produkci

### Po migraci
- [ ] **Otestovat řazení podle decimal polí**
- [ ] Otestovat všechny JsonSpecification filtry
- [ ] Ověřit výkon dotazů
- [ ] Spustit kompletní testovací sadu

## 📊 Výhody MS SQL Server oproti SQLite

### Podporované funkce v MS SQL Server
✅ **Decimal řazení** - Přímé řazení podle decimal typů  
✅ **Pokročilé indexy** - Lepší výkon pro složité dotazy  
✅ **Stored procedures** - Možnost optimalizace na databázové úrovni  
✅ **Pokročilé datové typy** - Plná podpora všech .NET typů  
✅ **Transakce** - Robustnější transakční podpora  
✅ **Škálovatelnost** - Lepší výkon pro velké objemy dat  

### Očekávané zlepšení výkonu
- **Rychlejší dotazy** s komplexními filtry
- **Lepší indexování** pro JsonSpecification filtry
- **Optimalizované řazení** bez konverzí typů
- **Paralelní zpracování** pro velké datasety

## 🔧 Doporučené akce

### Krátkodobě (před migrací)
1. **Označit všechny SQLite úpravy** komentáři `TODO: PRODUKCE`
2. **Dokumentovat výkon** současných dotazů pro porovnání
3. **Připravit testovací scénáře** pro ověření funkcionality

### Dlouhodobě (po migraci)
1. **Odstranit SQLite specifické úpravy**
2. **Optimalizovat dotazy** pro MS SQL Server
3. **Implementovat Include operace** pro eager loading
4. **Přidat indexy** pro často používané filtry

## 📝 Poznámky pro vývojáře

### Při přidávání nových funkcí
- Vždy testovat na obou databázích (pokud možno)
- Označovat SQLite specifické úpravy komentáři
- Dokumentovat omezení v kódu

### Při code review
- Kontrolovat SQLite specifické úpravy
- Ověřovat, že jsou správně označené
- Diskutovat alternativní řešení pro MS SQL Server

## 🎯 Závěr

SQLite slouží skvěle pro vývoj, ale MS SQL Server poskytne v produkci:
- **Lepší výkon** pro komplexní dotazy
- **Plnou funkcionalnost** bez omezení
- **Škálovatelnost** pro růst aplikace

Všechny SQLite specifické úpravy jsou jasně označené a připravené k odstranění při migraci na MS SQL Server.

---

**Vytvořeno:** $(date +%Y-%m-%d)  
**Verze:** 1.0  
**Autor:** DataCapture Development Team
