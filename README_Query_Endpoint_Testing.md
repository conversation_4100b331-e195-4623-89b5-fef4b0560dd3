# 🧪 Testování POST /entities/query endpointů

Tento balíček obsahuje kompletní dokumentaci a testovací nástroje pro univerzální dotazovací endpoint `POST /entities/query` v DataCapture API.

## 🎉 NOVÉ: JsonSpecification řešení

**Problém s Expression serializací byl vyřešen!** Implementovali jsme `JsonSpecification<T>` třídu a `SpecificationJsonConverter`, které umož<PERSON>ují skutečné filtrování přes JSON payloady.

## 📁 Obsah balíčku

### 📋 Dokumentace
- **`API_Query_Endpoint_Working_Examples.md`** - ✨ **NOVÉ** - Funkční příklady s JsonSpecification
- **`API_Query_Endpoint_Examples.md`** - Původní dokumentace (omezení Expression serializace)
- **`Filter_DTO_Implementation_Proposal.md`** - Alternativní návrh filter DTO objektů
- **`README_Query_Endpoint_Testing.md`** - Tento soubor (přehled)

### 🧪 Testovací skripty
- **`test_json_specification_endpoints.ps1`** - ✨ **NOVÉ** - PowerShell testy pro JsonSpecification
- **`test_json_specification_endpoints.sh`** - ✨ **NOVÉ** - Bash testy pro JsonSpecification
- **`test_query_endpoints.ps1`** - Původní PowerShell skript (základní testy)
- **`test_query_endpoints.sh`** - Původní Bash skript (základní testy)

### 💻 Implementace
- **`Application/Features/Generic/JsonSpecification.cs`** - ✨ **NOVÉ** - JSON-serializovatelná specifikace
- **`Infrastructure/Conversions/SpecificationJsonConverter.cs`** - ✨ **NOVÉ** - JSON converter

## 🚀 Rychlý start

### ✨ DOPORUČENO: JsonSpecification testy (skutečné filtrování)

#### Windows (PowerShell)
```powershell
# Základní test s JsonSpecification
.\test_json_specification_endpoints.ps1

# Test s vlastním URL
.\test_json_specification_endpoints.ps1 -BaseUrl "https://api.example.com"

# Test s verbose výstupem
.\test_json_specification_endpoints.ps1 -Verbose
```

#### Linux/Mac (Bash)
```bash
# Základní test s JsonSpecification
./test_json_specification_endpoints.sh

# Test s vlastním URL
./test_json_specification_endpoints.sh "https://api.example.com"

# Test s verbose výstupem
./test_json_specification_endpoints.sh "http://localhost:5000" "true"
```

### 📋 Původní testy (pouze základní funkcionalita)

#### Windows (PowerShell)
```powershell
# Základní test s výchozím URL
.\test_query_endpoints.ps1

# Test s vlastním URL
.\test_query_endpoints.ps1 -BaseUrl "https://api.example.com"
```

#### Linux/Mac (Bash)
```bash
# Základní test s výchozím URL
./test_query_endpoints.sh

# Test s vlastním URL
./test_query_endpoints.sh "https://api.example.com"
```

### ✨ Manuální testování s JsonSpecification (curl)
```bash
# Základní test SampleEntity - prázdná specifikace
curl -X POST "https://localhost:7003/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d "{}"

# Filtrování aktivních záznamů
curl -X POST "https://localhost:7003/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyFilters": [
      { "propertyName": "IsActive", "value": true }
    ]
  }'

# Textové vyhledávání s řazením
curl -X POST "https://localhost:7003/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "textSearchFilters": [
      { "propertyName": "Name", "searchText": "test" }
    ],
    "orderByFilter": {
      "propertyName": "Name",
      "descending": false
    }
  }'

# Komplexní filtr s stránkováním
curl -X POST "https://localhost:7003/v1/samples/query" \
  -H "Content-Type: application/json" \
  -d '{
    "propertyFilters": [
      { "propertyName": "IsActive", "value": true }
    ],
    "rangeFilters": [
      { "propertyName": "Age", "minValue": 18, "maxValue": 65 }
    ],
    "orderByFilter": {
      "propertyName": "CreatedAt",
      "descending": true
    },
    "skip": 0,
    "take": 10,
    "isPagingEnabled": true
  }'
```

## 🎉 JsonSpecification - Vyřešené omezení!

**Expression objekty se nyní dají serializovat do JSON!**

Implementace `JsonSpecification<T>` a `SpecificationJsonConverter` umožňuje:

✅ **Plně funkční:**
- ✅ **Skutečné filtrování** pomocí PropertyFilters, RangeFilters, TextSearchFilters
- ✅ **Řazení** pomocí OrderByFilter
- ✅ **Eager loading** pomocí IncludeProperties
- ✅ **Stránkování** pomocí skip, take, isPagingEnabled
- ✅ **Datové filtry** pomocí DateRangeFilters
- ✅ **Kombinace filtrů** - všechny filtry se kombinují pomocí AND

### Dostupné typy filtrů:
- **PropertyFilters** - Filtrování podle rovnosti
- **RangeFilters** - Filtrování podle rozsahu hodnot
- **TextSearchFilters** - Textové vyhledávání (Contains)
- **DateRangeFilters** - Filtrování podle rozsahu dat
- **OrderByFilter** - Řazení podle vlastnosti
- **IncludeProperties** - Eager loading souvisejících entit

## 🔧 Dostupné endpointy

| Entita | Endpoint | Popis |
|--------|----------|-------|
| SampleEntity | `POST /v1/samples/query` | Ukázková entita |
| Order | `POST /v1/orders/query` | Objednávky |
| OrderItem | `POST /v1/order-items/query` | Položky objednávek |
| Invoice | `POST /v1/invoices/query` | Faktury |

## 📊 Query parametry

| Parametr | Typ | Popis | Výchozí |
|----------|-----|-------|---------|
| `pageNumber` | int? | Číslo stránky (od 1) | null |
| `pageSize` | int? | Velikost stránky | null |
| `useCache` | bool | Použít cache | false |

## 🎯 Očekávané odpovědi

### Bez stránkování
```json
[
  {
    "id": 1,
    "name": "Záznam",
    "isActive": true,
    "createdAt": "2024-01-01T10:00:00"
  }
]
```

### Se stránkováním
```json
{
  "data": [...],
  "pageNumber": 1,
  "pageSize": 10,
  "totalCount": 25,
  "totalPages": 3,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```

## 🔮 Budoucí řešení

Pro skutečné filtrování doporučujeme implementovat jednoduché Filter DTO objekty podle návrhu v `Filter_DTO_Implementation_Proposal.md`.

**Příklad budoucího použití:**
```json
POST /v1/samples/query
{
  "nameFilter": "test",
  "isActive": true,
  "sortBy": "Name",
  "pageNumber": 1,
  "pageSize": 10
}
```

## 🛠️ Požadavky

### Pro testovací skripty
- **Windows:** PowerShell 5.1+
- **Linux/Mac:** bash, curl, bc (volitelně jq pro lepší výstup)

### Pro API
- DataCapture aplikace běžící na zadané URL
- Databáze s testovacími daty
- Správně registrované entity v DI kontejneru

## 📈 Interpretace výsledků testů

### ✅ Úspěšný test
- HTTP 200 odpověď
- Validní JSON struktura
- Počet záznamů (pokud je dostupný)

### ❌ Neúspěšný test
- HTTP chyba (4xx, 5xx)
- Síťová chyba
- Neplatná JSON odpověď

### 🔍 Časté problémy
1. **Aplikace neběží** - zkontrolujte URL a port
2. **Databáze není dostupná** - zkontrolujte connection string
3. **Entity nejsou registrované** - zkontrolujte DI konfiguraci
4. **CORS problémy** - zkontrolujte CORS nastavení

## 📞 Podpora

Pokud narazíte na problémy:

1. **Zkontrolujte logy aplikace** pro detailní chybové zprávy
2. **Otevřete Swagger UI** na `/swagger` pro interaktivní testování
3. **Ověřte databázové připojení** a dostupnost dat
4. **Zkontrolujte síťové připojení** k API

## 🔄 Aktualizace

Tento balíček bude aktualizován po implementaci Filter DTO objektů. Sledujte:
- Nové filtrovací možnosti
- Rozšířené testovací scénáře
- Vylepšené chybové zprávy
- Dodatečné entity

---

**Vytvořeno:** $(date +%Y-%m-%d)  
**Verze:** 1.0  
**Autor:** DataCapture API Team
